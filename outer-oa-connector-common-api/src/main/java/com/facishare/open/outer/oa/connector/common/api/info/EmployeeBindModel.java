package com.facishare.open.outer.oa.connector.common.api.info;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Objects;

@Data
public class EmployeeBindModel implements Serializable {
    private FsEmployee fsEmployee;
    private OutEmployee outEmployee;

    @Data
    public static class OutEmployee implements Serializable {
        private String userId;
        private String userName;
        private String departmentId;
        private String departmentName;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            OutEmployee outEmployee = (OutEmployee) o;
            return (StringUtils.equalsIgnoreCase(this.userId,outEmployee.getUserId()));
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId);
        }
    }

    @Data
    public static class FsEmployee extends OutEmployee  {
        private Integer status;
        private String mobile;
        private String email;
    }
}
