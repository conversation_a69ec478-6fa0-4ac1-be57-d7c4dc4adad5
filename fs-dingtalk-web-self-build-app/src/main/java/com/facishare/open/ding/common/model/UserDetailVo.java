package com.facishare.open.ding.common.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/8/3 10:05
 * @Version 1.0
 */
@Data
public class UserDetailVo  implements Serializable {

    private Integer errcode;
    private String errmsg;
    private Boolean hasMore;
    private List<useItem> userlist;
     @Data
     public class useItem{
        private String userid;
        private String unionid;
        private String mobile;
        private String workPlace;
        private String tel;
        private String remark;
        private Long order;
        private String openId;
        private Boolean isAdmin;
        private Boolean isBoss;
        private Boolean isHide;
        private Boolean isLeader;
        private Boolean active;
        private String name;
        private String position;
        private String email;
        private String avatar;
        private String jobnumber;
        private String  stateCode;
        private List<Long> department;
        private Integer sexType;
        private Long mainDepartment;
    }
}
