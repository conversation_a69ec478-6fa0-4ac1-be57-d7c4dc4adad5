package com.facishare.open.ding.transfer.handler;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.utils.SecurityUtil;
import com.facishare.open.oa.base.dbproxy.manager.DescManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.common.api.admin.DingConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 钉钉自建应用企业绑定迁移处理器
 * <AUTHOR>
 * @date 2024/2/21 15:46:25
 * @IgnoreI18nFile 迁移代码, 不做多语
 */
@Component
public class DingTalkSelfBuildEnterpriseBindHandler extends DingTalkSelfBuildHandler<DingEnterprise, OuterOaEnterpriseBindEntity> {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private DingEnterpriseDao dingEnterpriseDao;

    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;

    @Autowired
    private I18NStringManager i18NStringManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Autowired
    private DescManager descManager;

    @Override
    protected void update(int enterpriseId, DingEnterprise sourceData, OuterOaEnterpriseBindEntity targetData) {
        if (Objects.isNull(targetData)) {
            final String fsEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
            targetData = getOuterOaEnterpriseBindEntity(sourceData, fsEa + "__delete");
        }
        final OuterOaEnterpriseBindEntity entity = convert2EnterpriseBindEntity(sourceData, targetData);
        if (Objects.isNull(targetData)) {
            outerOaEnterpriseBindMapper.insert(entity);
        } else {
            outerOaEnterpriseBindMapper.updateById(entity);
        }

        final OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, entity.getFsEa(), entity.getAppId());
        initConfig(enterpriseBindEntity, sourceData);
    }

    private void initConfig(OuterOaEnterpriseBindEntity enterpriseBindEntity, DingEnterprise sourceData) {
        saveOuterConfig(enterpriseBindEntity, OuterOaConfigInfoTypeEnum.OUTER_SYSTEM_OBJECT_FIELDS, "[{\"fieldApiName\":\"vice_departments\",\"fieldLabel\":\"附属部门\",\"fieldType\":\"vice_departments\"},{\"fieldApiName\":\"manager_userid\",\"fieldLabel\":\"汇报对象\",\"fieldType\":\"employee\"},{\"fieldApiName\":\"name\",\"fieldLabel\":\"系统名（昵称）\",\"fieldType\":\"text\"},{\"fieldApiName\":\"phone\",\"fieldLabel\":\"手机\",\"fieldType\":\"text\"},{\"fieldApiName\":\"email\",\"fieldLabel\":\"邮箱\",\"fieldType\":\"text\"},{\"fieldApiName\":\"title\",\"fieldLabel\":\"职 位\",\"fieldType\":\"text\"},{\"fieldApiName\":\"userid\",\"fieldLabel\":\"用户ID\",\"fieldType\":\"text\"},{\"fieldApiName\":\"deptId\",\"fieldLabel\":\"主属部门\",\"fieldType\":\"department\"},{\"fieldApiName\":\"jobNumber\",\"fieldLabel\":\"员工编号\",\"fieldType\":\"text\"},{\"fieldApiName\":\"manager_userid\",\"fieldLabel\":\"直属主管\",\"fieldType\":\"employee\"}]");

        saveOuterConfig(enterpriseBindEntity, OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, "{\"itemFieldMappings\":[{\"crmFieldApiName\":\"name\",\"crmFieldLabel\":\"系统名（昵称）\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerDataText\":\"text1\",\"outerOAFieldApiName\":\"name\",\"outerOAFieldLabel\":\"系统名（昵称）\"},{\"crmFieldApiName\":\"phone\",\"crmFieldLabel\":\"手机\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerDataText\":\"text2\",\"outerOAFieldApiName\":\"phone\",\"outerOAFieldLabel\":\"手机\"},{\"crmFieldApiName\":\"full_name\",\"crmFieldLabel\":\"姓名\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerDataText\":\"text3\",\"outerOAFieldApiName\":\"name\",\"outerOAFieldLabel\":\"系统名（昵称）\"},{\"crmFieldApiName\":\"main_department\",\"crmFieldLabel\":\"主属部门\",\"fieldType\":\"department\",\"matchUnique\":false,\"outerDataText\":\"text4\",\"outerOAFieldApiName\":\"deptId\",\"outerOAFieldLabel\":\"主属部门\"},{\"crmFieldApiName\":\"employee_number\",\"crmFieldLabel\":\"员工编号\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerOAFieldApiName\":\"jobNumber\",\"outerOAFieldLabel\":\"员工编号\"},{\"crmFieldApiName\":\"position\",\"crmFieldLabel\":\"职位\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerOAFieldApiName\":\"title\",\"outerOAFieldLabel\":\"职 位\"},{\"crmFieldApiName\":\"email\",\"crmFieldLabel\":\"邮箱\",\"fieldType\":\"text\",\"matchUnique\":false,\"outerOAFieldApiName\":\"email\",\"outerOAFieldLabel\":\"邮箱\"},{\"crmFieldApiName\":\"leader\",\"crmFieldLabel\":\"汇报对象\",\"fieldType\":\"employee\",\"matchUnique\":false,\"outerOAFieldApiName\":\"manager_userid\",\"outerOAFieldLabel\":\"直属主管\"},{\"crmFieldApiName\":\"vice_departments\",\"crmFieldLabel\":\"附属部门\",\"fieldType\":\"vice_departments\",\"matchUnique\":false,\"outerOAFieldApiName\":\"vice_departments\",\"outerOAFieldLabel\":\"附属部门\"}]}");

        saveSettingsResult(enterpriseBindEntity, sourceData);
    }



    private Integer saveOuterConfig(OuterOaEnterpriseBindEntity enterpriseBindEntity, OuterOaConfigInfoTypeEnum type, String configInfo) {
        final OuterOaConfigInfoEntity entity = new OuterOaConfigInfoEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setDcId(enterpriseBindEntity.getId());
        entity.setFsEa(enterpriseBindEntity.getFsEa());
        entity.setOutEa(enterpriseBindEntity.getOutEa());
        entity.setAppId(enterpriseBindEntity.getAppId());
        entity.setType(type);
        entity.setConfigInfo(configInfo);
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());

        return outerOaConfigInfoManager.upsertByDcIdAndType(entity);
    }

    private Integer saveSettingsResult(OuterOaEnterpriseBindEntity enterpriseBindEntity, DingEnterprise sourceData) {
        final String dcId = enterpriseBindEntity.getId();
        final SettingsResult settingsResult = convert2SettingsResult(sourceData, dcId);

        return saveOuterConfig(enterpriseBindEntity, OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, JSON.toJSONString(settingsResult));
    }

    private static SettingsResult convert2SettingsResult(DingEnterprise sourceData, String dcId) {
        final SettingsResult settingsResult = new SettingsResult();
        settingsResult.setCurrentDcId(dcId);
        final EnterpriseConfigAccountSyncTypeEnum syncTypeEnum = Objects.equals(sourceData.getDevModel(), 1) ? EnterpriseConfigAccountSyncTypeEnum.accountBind : EnterpriseConfigAccountSyncTypeEnum.accountSync;
        settingsResult.setSyncTypeEnum(syncTypeEnum);
        final BindTypeEnum bindTypeEnum;
        if (Objects.equals(EnterpriseConfigAccountSyncTypeEnum.accountSync, syncTypeEnum)) {
            bindTypeEnum = BindTypeEnum.manual;
        } else if (sourceData.getAutBind() == null) {
            bindTypeEnum = null;
        } else {
            bindTypeEnum = sourceData.getAutBind() == 1 ? BindTypeEnum.auto : BindTypeEnum.manual;
        }
        settingsResult.setBindTypeEnum(bindTypeEnum);

        final SettingAccountRulesModel.EmployeeRangeRemoveRule employeeRangeRemoveRule = new SettingAccountRulesModel.EmployeeRangeRemoveRule();
        employeeRangeRemoveRule.setUnbind(false);
        employeeRangeRemoveRule.setStopEmp(false);
        settingsResult.setEmployeeRangeRemoveRule(employeeRangeRemoveRule);
        final SettingAccountRulesModel.EmployeeLeaveRule employeeLeaveRule = new SettingAccountRulesModel.EmployeeLeaveRule();
        employeeLeaveRule.setUnbind(true);
        employeeLeaveRule.setStopEmp(true);
        settingsResult.setEmployeeLeaveRule(employeeLeaveRule);

        return settingsResult;
    }

//    private static SettingsResult.DeptFieldMapping getDeptFieldMapping(String name, CRMEmployeeFiledEnum filedEnum) {
//        return new SettingsResult.DeptFieldMapping(name, filedEnum.getDesc(), filedEnum.getCode(), filedEnum.getDesc());
//    }
//
//    private static SystemFieldMappingResult.ItemFieldMapping getEmployeeFieldMapping(String name, CRMEmployeeFiledEnum filedEnum, Integer index, Boolean matchUnique) {
//        return new SystemFieldMappingResult.ItemFieldMapping(filedEnum.getCode(), filedEnum.getDesc(), name, filedEnum.getDesc(), "text" + index, matchUnique,  FieldTypeEnum.text);
//    }

    private OuterOaEnterpriseBindEntity convert2EnterpriseBindEntity(DingEnterprise sourceData, OuterOaEnterpriseBindEntity targetData) {
        final DingConnectorVo connectInfo = DingEnterprise.convert2ConnectInfo(sourceData, i18NStringManager);
        connectInfo.setDataCenterName(i18NStringManager.getByEi(I18NStringEnum.cxb004, String.valueOf(sourceData.getEi())));
        connectInfo.setConnectorName(i18NStringManager.getByEi(I18NStringEnum.cxb004, String.valueOf(sourceData.getEi())));

        final OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
        entity.setId(Objects.isNull(targetData) ? sourceData.getId().toString() : targetData.getId());
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(eieaConverter.enterpriseIdToAccount(sourceData.getEi()));
        entity.setOutEa(sourceData.getDingCorpId());
        entity.setAppId(sourceData.getAppKey());
        entity.setConnectInfo(JSON.toJSONString(connectInfo));
        entity.setBindType(BindTypeEnum.manual);
        entity.setBindStatus(BindStatusEnum.normal);
        entity.setCreateTime(Objects.isNull(targetData) ? sourceData.getCreateTime().getTime() : targetData.getCreateTime());
        entity.setUpdateTime(Objects.isNull(targetData) ? sourceData.getUpdateTime().getTime() : targetData.getUpdateTime());

        return entity;
    }

    @Override
    protected boolean checkDataEquals(DingEnterprise sourceData, OuterOaEnterpriseBindEntity targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        // 在新页面保存了的,以新页面数据为准
        if (targetData.getUpdateTime() > sourceData.getUpdateTime().getTime()) {
            return true;
        }

        final DingConnectorVo connectInfo = JSON.parseObject(targetData.getConnectInfo(), DingConnectorVo.class);
        if (Objects.isNull(connectInfo)) {
            return false;
        }
        // 检查字段是否相同
        if (!Objects.equals(connectInfo.getCreateBy(), sourceData.getCreateBy()) ||
                !Objects.equals(connectInfo.getUpdateBy(), sourceData.getUpdateBy()) ||
                !Objects.equals(connectInfo.getIsCallback(), sourceData.getIsCallback()) ||
                !Objects.equals(connectInfo.getAllIndex(), sourceData.getAllIndex()) ||
                !Objects.equals(connectInfo.getTodoType(), sourceData.getTodoType()) ||
                !Objects.equals(connectInfo.getAgentId(), sourceData.getAgentId()) ||
                !Objects.equals(connectInfo.getAppSecret(), sourceData.getAppSecret()) ||
                !Objects.equals(connectInfo.getRedirectAppId(), sourceData.getRedirectAppId()) ||
                !Objects.equals(connectInfo.getRedirectAppSecret(), sourceData.getRedirectAppSecret()) ||
                !Objects.equals(connectInfo.getClientIp(), sourceData.getClientIp()) ||
                !Objects.equals(connectInfo.isAlertConfig(), sourceData.convert2AlertConfig()) ||
                !Objects.equals(connectInfo.getAlertTypes(), sourceData.convert2AlertTypes())) {
            return false;
        }

        OuterOaConfigInfoEntity entity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, targetData.getId());
        if (Objects.isNull(entity)) {
            return false;
        }

        entity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, targetData.getId());
        return !Objects.isNull(entity);
    }

    @Override
    protected OuterOaEnterpriseBindEntity getTargetData(int enterpriseId, DingEnterprise k) {
        final String fsEa = eieaConverter.enterpriseIdToAccount(enterpriseId);
        return getOuterOaEnterpriseBindEntity(k, fsEa);
    }

    private OuterOaEnterpriseBindEntity getOuterOaEnterpriseBindEntity(DingEnterprise k, String fsEa) {
        final OuterOaEnterpriseBindEntity entity = new OuterOaEnterpriseBindEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(fsEa);
        entity.setAppId(String.valueOf(k.getAppKey()));
        LambdaQueryWrapper<OuterOaEnterpriseBindEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(entity);
        return outerOaEnterpriseBindMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    protected List<DingEnterprise> getSourceDataPage(Integer enterpriseId, DingEnterprise maxId) {
        if (Objects.nonNull(maxId)) {
            return new ArrayList<>();
        }
        final DingEnterprise byEI = dingEnterpriseDao.findByEI(enterpriseId);
        return byEI == null ? Lists.newArrayList() : Lists.newArrayList(decryptDingEnterprise(byEI));
    }

    private DingEnterprise decryptDingEnterprise(DingEnterprise dingEnterprise) {
        if (ObjectUtils.isEmpty(dingEnterprise)) {
            return dingEnterprise;
        }
        DingEnterprise copyDingEnterprise = new DingEnterprise();
        BeanUtils.copyProperties(dingEnterprise, copyDingEnterprise);
        copyDingEnterprise.setAppKey(SecurityUtil.decryptStr(dingEnterprise.getAppKey()));
        copyDingEnterprise.setAppSecret(SecurityUtil.decryptStr(dingEnterprise.getAppSecret()));
        copyDingEnterprise.setRedirectAppId(SecurityUtil.decryptStr(dingEnterprise.getRedirectAppId()));
        copyDingEnterprise.setRedirectAppSecret(SecurityUtil.decryptStr(dingEnterprise.getRedirectAppSecret()));
        copyDingEnterprise.setToken(SecurityUtil.decryptStr(dingEnterprise.getToken()));
        return copyDingEnterprise;
    }

    @Override
    protected void transferAfter(int enterpriseId) throws Throwable {
        appAuthEntityCache.invalidate(enterpriseId);
    }

    @Override
    public int getThreadNum() {
        return 10;
    }
}