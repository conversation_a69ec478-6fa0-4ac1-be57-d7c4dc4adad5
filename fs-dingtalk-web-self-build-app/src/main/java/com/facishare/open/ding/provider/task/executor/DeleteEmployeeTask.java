package com.facishare.open.ding.provider.task.executor;

import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class DeleteEmployeeTask implements Runnable {

    private Map<String, User> deleteMap;
    private Integer ei;
    private String appId;
    @Autowired
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    @Autowired
    private ObjectMappingService objectMappingService;

    public DeleteEmployeeTask(Map<String, User> deleteMap, Integer ei, String appId) {
        this.deleteMap = deleteMap;
        this.ei=ei;
        this.appId = appId;
    }

    @Override
    public void run() {
          if (MapUtils.isNotEmpty(deleteMap)) {
                              deleteMap.entrySet().forEach(item -> {
                                  DingMappingEmployeeResult dingMappingEmployee = new DingMappingEmployeeResult();
                                 dingMappingEmployee.setEi(ei);
                                  dingMappingEmployee.setDingEmployeeId(item.getValue().getUserid());
                                 //停用纷享员工
                                  DingMappingEmployeeResult mappingEmp = dingMappingEmployeeManager.findIsBindByDingId(dingMappingEmployee, appId);
                                  if (Objects.nonNull(mappingEmp) && Objects.nonNull(mappingEmp.getEmployeeId())) {
                                      Integer fxEmpId = mappingEmp.getEmployeeId();
                                      String fxEmpName = mappingEmp.getEmployeeName();
                                      objectMappingService.stopFxEmployee(ei, fxEmpId, fxEmpName, appId, item.getValue().getUserid());
                                  }
                              });
                          }
    }
}
