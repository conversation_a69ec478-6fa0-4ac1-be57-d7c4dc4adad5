//package com.facishare.open.ding.web.interceptor;
//
//import com.facishare.asm.api.auth.AuthXC;
//import com.facishare.asm.api.enums.ValidateStatus;
//import com.facishare.asm.api.model.CookieToAuth;
//import com.facishare.asm.api.service.ActiveSessionAuthorizeService;
//import com.facishare.open.ding.api.exception.DingtalkException;
//import com.facishare.open.ding.web.ajax.AjaxCode;
//import com.facishare.open.ding.web.base.UserContextHolder;
//import com.facishare.open.ding.web.base.UserVo;
//import com.facishare.uc.api.exeception.UserCenterException;
//import com.facishare.uc.api.model.fscore.SimpleEnterprise;
//import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
//import com.facishare.uc.api.model.fscore.result.GetSimpleEnterpriseResult;
//import com.facishare.uc.api.service.EnterpriseEditionService;
//import javax.servlet.http.Cookie;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
//
///**
// * <p>类的详细说明</p>
// * @dateTime 2018/7/17 15:14
// */
//public class UserInterceptors extends HandlerInterceptorAdapter {
//
//    public static final Logger logger = LoggerFactory.getLogger(UserInterceptors.class);
//
//    @Autowired
//    private ActiveSessionAuthorizeService activeSessionAuthorizeService;
//
//    @Autowired
//    private EnterpriseEditionService enterpriseEditionService;
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler){
//        String fsAuthXcookie = getFsAuthXCookie(request.getCookies());
//        logger.info("get cookie = {}",fsAuthXcookie);
//        String requestUri = request.getRequestURI();
//        if (requestUri.contains("/business")){
//            return true;
//        }
//        if (fsAuthXcookie != null) {
//            CookieToAuth.Argument argument = new CookieToAuth.Argument();
//            argument.setCookie(fsAuthXcookie);
//            argument.setFsToken(null);
//            argument.setIp(null);
//            CookieToAuth.Result<AuthXC> cookieToAuthResult = activeSessionAuthorizeService.cookieToAuthXC(argument);
//            if (!cookieToAuthResult.isSucceed()
//                    || !ValidateStatus.NORMAL.equals(cookieToAuthResult.getValidateStatus())) {
//                logger.warn("activeSessionAuthorizeService.cookieToAuthXC failed, argument={}, resultUser=[{}]", argument, cookieToAuthResult);
//                throw new DingtalkException(AjaxCode.USER_NOT_LOGIN, "验证用户登录信息失败");
//            }
//            AuthXC authXC = cookieToAuthResult.getBody();
//            logger.info("authXC={}",authXC);
//            if(StringUtils.isBlank(authXC.getEnterpriseAccount())){
//                return false;
//            }
//            if(authXC.getEmployeeId()==null||authXC.getEmployeeId()<0){
//                return false;
//            }
//
//            SimpleEnterprise simpleEnterprise = new SimpleEnterprise();
//            try {
//                GetSimpleEnterpriseArg getSimpleEnterpriseArg = new GetSimpleEnterpriseArg();
//                getSimpleEnterpriseArg.setEnterpriseAccount(authXC.getEnterpriseAccount());
//                getSimpleEnterpriseArg.setEnterpriseId(authXC.getEnterpriseId());
//                GetSimpleEnterpriseResult simpleEnterpriseResult = enterpriseEditionService.getSimpleEnterprise(getSimpleEnterpriseArg);
//                simpleEnterprise = simpleEnterpriseResult.getSimpleEnterprise();
//            } catch (UserCenterException e) {
//                logger.info("enterpriseEditionService.getSimpleEnterprise exception, authXC=[{}], result=[{}].", authXC, e.getMessage());
//            }
//
//            UserContextHolder.get().set(new UserVo(authXC, simpleEnterprise.getEnterpriseName(), simpleEnterprise.getEnterpriseShortName()));
//            return true;
//        }
////        UserVo userVo = new UserVo();
////        userVo.setEnterpriseAccount("datayes2017");
////        userVo.setEmployeeId(123);
////        UserContextHolder.get().set(userVo);
//        return true;
////        return false;
//    }
//
//    /**
//     * cookies中获取FsAuthX
//     * @param cookies
//     * @return
//     */
//    private String getFsAuthXCookie(Cookie[] cookies){
//        if (cookies == null || cookies.length < 0) {
//            return null;
//        } else {
//            for(Cookie cookie : cookies) {
//                if (cookie.getName().equals("FSAuthXC")){
//                    return cookie.getValue();
//                }
//            }
//            return null;
//        }
//    }
//}
