package com.facishare.open.ding.common.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/23 16:56
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
@Data
public class PageObject<T> implements Serializable {

	/** 请求页码 **/
	private Integer pageNo;

	/** 每页条数 **/
	private Integer pageSize;

	/** 总记录数 **/
	private Integer totalCount;

	/** 结果列表 **/
	private List<T> result;

}
