package com.facishare.open.ding.common.model;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-10-17 13:22
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class User  implements Serializable {
    private String unionid;
    private String userid;
    private String name;
    private String mobile;
    private String email;
    //职位
    private String position;
    //钉钉工号
    private String jobnumber;
    private String managerUserid;
    private Integer sexType;
    private Long mainDepartment;
    private String stateCode;
    /**
     * 入职时间
     * 信息面板中入职时间字段内有值才返回。
     * 第三方企业应用，不返回该参数。
     */
    private Long hiredDate;
}
