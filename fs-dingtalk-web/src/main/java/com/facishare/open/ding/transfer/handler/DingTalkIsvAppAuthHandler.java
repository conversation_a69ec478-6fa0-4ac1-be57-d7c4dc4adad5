package com.facishare.open.ding.transfer.handler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.open.ding.provider.arg.AppAuthEntity;
import com.facishare.open.ding.provider.dao.AppAuthDao;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.outer.oa.connector.common.api.params.DingtalkAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaAppInfoMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.fxiaoke.api.IdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/23 16:52:39
 */
@Component
public class DingTalkIsvAppAuthHandler extends DingTalkIsvHandler<AppAuthEntity, OuterOaAppInfoEntity> {

    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Autowired
    private OuterOaAppInfoMapper outerOaAppInfoMapper;

    @Autowired
    private AppAuthDao appAuthDao;

    @Override
    protected void update(int enterpriseId, AppAuthEntity sourceData, OuterOaAppInfoEntity targetData) {
        final OuterOaAppInfoEntity entity = convert2OuterOaAppInfoEntity(sourceData, targetData);
        outerOaAppInfoManager.upsert(entity);
    }

    private OuterOaAppInfoEntity convert2OuterOaAppInfoEntity(AppAuthEntity sourceData, OuterOaAppInfoEntity targetData) {
        final OuterOaAppInfoEntity info = new OuterOaAppInfoEntity();
        info.setId(Objects.isNull(targetData) ? IdGenerator.get() : targetData.getId());
        info.setChannel(ChannelEnum.dingding);
        info.setOutEa(sourceData.getDingCorpId());
        info.setAppId(String.valueOf(sourceData.getAppId()));
        info.setAppType(OuterOaAppInfoTypeEnum.isv);
        info.setAppInfo(convert2AppInfo(sourceData));
        info.setStatus(OuterOaAppInfoStatusEnum.normal);
        info.setCreateTime(Objects.isNull(targetData) ? System.currentTimeMillis() : targetData.getCreateTime());
        info.setUpdateTime(System.currentTimeMillis());

        return info;
    }

    private String convert2AppInfo(AppAuthEntity sourceData) {
        return convert2AppInfo(sourceData.getAgentId(), sourceData.getSuiteId(), sourceData.getAuthInfo());
    }

    private String convert2AppInfo(Long agentId, Long suiteId, String authInfo) {
        final DingtalkAppInfoParams params = new DingtalkAppInfoParams();
        params.setAgentId(String.valueOf(agentId));
        params.setSuiteId(suiteId);
        params.setAuthInfo(authInfo);

        return JSON.toJSONString(params);
    }

    @Override
    protected boolean checkDataEquals(AppAuthEntity sourceData, OuterOaAppInfoEntity targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }
        return Objects.equals(convert2AppInfo(sourceData), targetData.getAppInfo()) &&
                Objects.equals(OuterOaAppInfoStatusEnum.normal, targetData.getStatus());
    }

    @Override
    protected OuterOaAppInfoEntity getTargetData(int enterpriseId, AppAuthEntity k) {
        final OuterOaAppInfoEntity info = new OuterOaAppInfoEntity();
        info.setChannel(ChannelEnum.dingding);
        info.setAppType(OuterOaAppInfoTypeEnum.isv);
        info.setAppId(String.valueOf(k.getAppId()));
        info.setOutEa(k.getDingCorpId());
        LambdaQueryWrapper<OuterOaAppInfoEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(info);
        return outerOaAppInfoMapper.selectOne(lambdaQueryWrapper);
    }

    @Override
    protected List<AppAuthEntity> getSourceDataPage(Integer enterpriseId, AppAuthEntity maxId) {
        Long id = Objects.isNull(maxId) ? null : maxId.getId();
        return appAuthDao.pageById(enterpriseId, id, 1000);
    }
}
