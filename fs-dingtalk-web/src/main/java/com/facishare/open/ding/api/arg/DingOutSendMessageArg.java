package com.facishare.open.ding.api.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.A;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/26 16:28
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingOutSendMessageArg implements Serializable {
    private String dingCorpId;
    private List<String> dingEmpIds;
    private Map<String,Object> dataMap;
    private String templateId;
    private String suiteId;
}
