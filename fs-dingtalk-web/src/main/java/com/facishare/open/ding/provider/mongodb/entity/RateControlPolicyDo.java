package com.facishare.open.ding.provider.mongodb.entity;

import java.util.List;
import lombok.Data;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Id;

/**
 * <p>类的详细说明</p>
 * @dateTime 2018/7/11 11:48
 * <AUTHOR> <EMAIL>
 * @version 1.0 
 */
@Data
@Entity(value = "rate_control_policy", noClassnameStored = true)
public class RateControlPolicyDo extends BaseObject {

    private static final long serialVersionUID = -1033097224774681572L;

    @Id
    private ObjectId id;

    String policyBusiness;
    String policySerial;
    boolean status;
    List<String> paramList;

   /* public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getPolicyBusiness() {
        return policyBusiness;
    }

    public void setPolicyBusiness(String policyBusiness) {
        this.policyBusiness = policyBusiness;
    }

    public String getPolicySerial() {
        return policySerial;
    }

    public void setPolicySerial(String policySerial) {
        this.policySerial = policySerial;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public List<String> getParamList() {
        return paramList;
    }

    public void setParamList(List<String> paramList) {
        this.paramList = paramList;
    }*/


}
