package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.dto.OutDepartmentInfoDto;
import com.facishare.open.ding.api.enums.BindType;
import com.facishare.open.ding.api.model.AuthEnterPriseModel;
import com.facishare.open.ding.api.model.FreeOrderModel;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.UserAppResult;
import com.facishare.open.ding.api.service.*;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.service.cloud.CloudEmpService;
import com.facishare.open.ding.api.vo.AppAuthVo;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.UserMappingVo;
import com.facishare.open.ding.cloud.arg.CreateCrmOrderArg;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.manager.CrmManager;
import com.facishare.open.ding.cloud.manager.DataPushManager;
import com.facishare.open.ding.cloud.manager.DingManager;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.cloud.utils.PinyinUtils;
import com.facishare.open.ding.cloud.utils.RedisLockUtils;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.redis.RedisDataSource;
import com.facishare.open.ding.remote.OrganizationService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.constants.GlobalValue;
import com.facishare.restful.common.StopWatch;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/5/6 0:10 应用授权
 * @Version 1.0
 */
@Service("enterpriseAuthBizType4ServiceImpl")
@EnableRetry
@Slf4j
public class EnterpriseAuthBizType4ServiceImpl implements DingEventService {

    /**
     * 钉钉云企业
     */
    private static final String ENTERPRISE_PREFIX = "ddqy";

    @Autowired
    private CrmManager crmManager;
    @Autowired
    private DingManager dingManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private CloudEmpService cloudEmpService;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private UserAppMappingService userAppMappingService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Autowired
    private CloudDeptService cloudDeptService;
    @Autowired
    private CrmSyncObjService crmSyncObjService;
    @Autowired
    private DataPushManager dataPushManager;
    @Autowired
    private OrganizationService organizationService;
    public static String AUTH_SCOPE="AUTH_SCOPE_%S";
    private Integer NATURE_STATUE=0;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Retryable( value = Exception.class,maxAttempts = 3, backoff = @Backoff(delay = 3000, multiplier = 2))
    @Override
    public void executeEvent(HighBizDataDo bizDataDo)  {
        StopWatch stopWatch = StopWatch.create("bizType4");
        log.info("retry auth 4 bizData :{}",bizDataDo.getCorpId());
        if (StringUtils.isEmpty(bizDataDo.getBizData())) {
            return;
        }
        String suiteId=bizDataDo.getSubscribeId().split("_0")[0];
        //添加分布式锁
        if(RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(),String.format(AUTH_SCOPE,bizDataDo.getCorpId()), bizDataDo.getCorpId(), 3)){
            AuthEnterPriseModel.BizData authModel = JSONObject.parseObject(bizDataDo.getBizData(), new TypeReference<AuthEnterPriseModel.BizData>() {
            });
            if(authModel.getSyncAction().equals("org_suite_relieve")){
                log.info("auth service relieve:{}",bizDataDo.getCorpId());
                return;
            }
            String dingCorpId = bizDataDo.getCorpId();
            Long appCode = authModel.getAuthInfo().getAgent().get(0).getAppId();
            //判断企业是否已经在其他应用创建过
            Result<List<DingCorpMappingVo>> dingCorpMapping = dingCorpMappingService.queryCorpMappingByCorpId(dingCorpId, String.valueOf(appCode));
//            log.info("crmResult eventType dingCorpId:{},result:{}",dingCorpId,crmResult);
            if(ObjectUtils.isEmpty(dingCorpMapping.getData())){
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(),String.format(AUTH_SCOPE,bizDataDo.getCorpId()),bizDataDo.getCorpId());
                return;
            }
            DingCorpMappingVo crmResult = dingCorpMapping.getData().get(0);
            Integer ei=crmResult.getEi();
            Result<Integer> appAuth = insertAppAuth(authModel, ei, bizDataDo.getSubscribeId());
            log.info("event insert appAuth:{},model:{},result:{}",appAuth,authModel,crmResult);
            if (NATURE_STATUE.equals(crmResult.getIsInit())) {
                //TODO 查询部门999999有没有创建，不采用监听创建企业的MQ。因为预设部门也是在同步MQ
                Boolean IS_INIT_DEPT=Boolean.TRUE;
                do {
                    Result<Map<String, Object>> crmDeptResult = crmManager.queryCrmDept(ei, "_id", "999999");
                    if(ObjectUtils.isEmpty(crmDeptResult.getData())){
                        try {
                            log.info("waiting initDept create:{}", bizDataDo.getCorpId());
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }else {
                        IS_INIT_DEPT=false;
                    }
                } while (IS_INIT_DEPT);
                initOrganization(authModel, crmResult,suiteId);
                log.info("init initOrganization result finish crmResult:{}",crmResult);
                //修改ding_corp_mapping的init
                dingCorpMappingService.updateInitStatus(ei);
                log.info("init organzation finsh ei:{}",ei);


            }else{
                Result<DingCorpMappingVo> mappingResult = dingCorpMappingService.queryMappingByAppId(dingCorpId, appCode);
                if (ObjectUtils.isEmpty(mappingResult.getData())) {
                    //新增的授权应用，需要为授权范围内的员工，添加新的角色
                    insertCorpMapping(crmResult.getEa(), crmResult.getEi(), authModel, bizDataDo.getSubscribeId(), 1);
                }
                updateScopeRole(authModel,suiteId,ei);
            }
            //组织架构同步完成后，调用ERP平台预置钉钉渠道的erp集成
//            if(ConfigCenter.INIT_SUITE_ID_CONNECT_CHANNEL.contains(suiteId)) {
//                if (crmSyncObjService.queryIsSyncData(dingCorpId) <= 0) {
//                    // 调用预置erp渠道
//                    try {
//                        Result<String> settingResult = dataPushManager.initErpSetting(String.valueOf(ei));
//                        if(settingResult.isSuccess()){
//                            //查询钉钉的绑定的员工，插入erp员工表
//                            List<ErpEmployeeArg> erpEmployeeArgs = Lists.newArrayList();
//                            Result<List<DingMappingEmployeeResult>> allEmpResult = objectMappingService.batchGetEmpIds(ei, null);
//                            allEmpResult.getData().forEach(item -> {
//                                ErpEmployeeArg erpEmployeeArg = new ErpEmployeeArg();
//                                erpEmployeeArg.setErpEmployeeId(item.getDingEmployeeId());
//                                erpEmployeeArg.setErpEmployeeName(item.getDingEmployeeName());
//                                erpEmployeeArg.setFsEmployeeId(item.getEmployeeId());
//                                erpEmployeeArgs.add(erpEmployeeArg);
//                            });
//                            dataPushManager.batchSaveEmployeeData(String.valueOf(ei), erpEmployeeArgs);
//                            //初始化历史数据同步配置
//                            crmSyncObjService.initSetting(dingCorpId, ei);
//                        }
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
        }
        stopWatch.lap("updateScope");
        stopWatch.log();
        return;
    }

    private Result<Integer> insertCorpMapping(String ea, Integer ei, AuthEnterPriseModel.BizData bizData, String suiteId, Integer initOrganization) {
        String dingCorpId = bizData.getAuthCorpInfo().getCorpId();
        Long agentId = bizData.getAuthInfo().getAgent().get(0).getAgentId();
        Long appCode = bizData.getAuthInfo().getAgent().get(0).getAppId();
        String authInfo = JSONObject.toJSONString(bizData.getAuthScope().getAuthOrgScopes());
        DingCorpMappingVo corpMappingVo = DingCorpMappingVo.builder().ea(ea).ei(ei).dingCorpId(dingCorpId).enterpriseName(bizData.getAuthCorpInfo().getCorpName())
                .appCode(appCode).bindType(BindType.ORDER_BIND.getType()).isInit(initOrganization).extend(GlobalValue.enterprise_extend).build();
        Result<Integer> corpCount = dingCorpMappingService.insertCorpMapping(corpMappingVo);
        Long suffix = Long.valueOf(Splitter.on("_").splitToList(suiteId).get(0));
        AppAuthVo appAuthVo = AppAuthVo.builder().appId(appCode).agentId(agentId).dingCorpId(dingCorpId).ei(ei).authInfo(authInfo).suiteId(suffix).build();
        appAuthService.insertAuth(appAuthVo);
        return corpCount;
    }

    private Result<Integer> insertAppAuth(AuthEnterPriseModel.BizData bizData,Integer ei,String suiteId){
        log.info("insert appAuth:{}",bizData);
        String dingCorpId = bizData.getAuthCorpInfo().getCorpId();
        Long agentId = bizData.getAuthInfo().getAgent().get(0).getAgentId();
        Long appCode = bizData.getAuthInfo().getAgent().get(0).getAppId();
        String authInfo = JSONObject.toJSONString(bizData.getAuthScope().getAuthOrgScopes());
        Long suffix = Long.valueOf(Splitter.on("_").splitToList(suiteId).get(0));
        AppAuthVo appAuthVo = AppAuthVo.builder().appId(appCode).agentId(agentId).dingCorpId(dingCorpId).ei(ei).authInfo(authInfo).suiteId(suffix).build();
        Result<Integer> insertAuth = appAuthService.insertAuth(appAuthVo);
        return insertAuth;
    }



    public void initOrganization(AuthEnterPriseModel.BizData authModel, DingCorpMappingVo corpMappingVo,String suiteId) {
        log.info("auth model:{}",authModel);
        //初始化部门

        //个人架构不同步组织架构，不必先查询部门范围
        //如果是个人组织，不同步组织架构
        Integer finalEi = corpMappingVo.getEi();
        Result<List<DingCorpMappingVo>> corpMappingResult = dingCorpMappingService.queryByEi(finalEi);
        if(ObjectUtils.isNotEmpty(corpMappingResult.getData().get(0).getDingMainCorpId())){
            log.info("person version not sync organization");
            return;
        }

        List<Dept> levelTravelDepts = dingManager.getListDeptByScope(authModel.getAuthScope().getAuthOrgScopes().getAuthedDept(), corpMappingVo.getDingCorpId(),suiteId);

        if(CollectionUtils.isNotEmpty(levelTravelDepts)){
            Result<Void> initDept = crmManager.obtainOrderDept(corpMappingVo.getEi(), authModel.getAuthCorpInfo().getCorpId(), levelTravelDepts,suiteId);
            log.info("init dept:{},corp_id:{}",initDept,corpMappingVo.getDingCorpId());
            //初始化员工,营销通只同步主管理员
            if(!ConfigCenter.MARKETING_SUITE_ID.equals(suiteId)){
                levelTravelDepts.forEach(item -> {
                    List<EmployeeDingVo> userList = dingManager.queryUserByDept(corpMappingVo.getDingCorpId(), item.getId(), suiteId);
                    //创建员工：
                    for (int i = 0; i < userList.size(); i++) {
                        EmployeeDingVo employeeDingVo = userList.get(i);
                        Result<DingMappingEmployeeResult> userResult = objectMappingService.queryEmpByDingUserId(finalEi, employeeDingVo.getUserid(), String.valueOf(corpMappingVo.getAppCode()));
                        if (ObjectUtils.isEmpty(userResult.getData())) {
                            Result<Integer> result = cloudEmpService.cloudCreateEmp(employeeDingVo, finalEi, corpMappingVo.getAppCode(), corpMappingVo.getDingCorpId());
                            if (result.getErrorCode() == ResultCode.ENTERPRISE_COUNT_FULL.getErrorCode()) break;
                        } else {
                            cloudEmpService.cloudUpdateEmp(employeeDingVo, finalEi, corpMappingVo.getAppCode(), corpMappingVo.getDingCorpId());
                        }
                    }
                    log.info("create emp dept id:{} ", item.getId());
                });
            }
        }
        //查询企业主管理员
        List<String> authUser=Lists.newArrayList();
        if(suiteId.equals(ConfigCenter.MARKETING_SUITE_ID)){
            //主管理员
           authUser = dingManager.getListAdmins(corpMappingVo.getDingCorpId(),suiteId);
        }else {
            //授权员工
             authUser = authModel.getAuthScope().getAuthOrgScopes().getAuthedUser();
        }
        log.info("event type 4 authUser:{}",authUser);
        List<EmployeeDingVo> authEmp = dingManager.getDingEmp(corpMappingVo.getDingCorpId(), authUser,suiteId);
        authEmp.stream().forEach(authItem ->{
            Result<Integer> authUserCount = cloudEmpService.cloudCreateEmp(authItem, finalEi, corpMappingVo.getAppCode(), corpMappingVo.getDingCorpId());
        });
        //修改之前的系统管理人员员工到对应的部门
        Result<List<DingMappingEmployeeResult>> crmResult = objectMappingService.batchQueryMapping(corpMappingVo.getEi(), Lists.newArrayList(1000), String.valueOf(corpMappingVo.getAppCode()));
        updateUser(crmResult.getData().get(0), corpMappingVo.getEi(), corpMappingVo.getAppCode(), corpMappingVo.getDingCorpId(),suiteId);
        //授权角色
        Result<List<Integer>> empList = objectMappingService.queryMappingCrm(corpMappingVo.getEi(), String.valueOf(corpMappingVo.getAppCode()));
        String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(corpMappingVo.getAppCode())).orElseGet(() -> "00000000000000000000000000000015");

        //TODO 需要计算当前的CRM配额，目前换成单个添加CRM角色
        for (Integer datum : empList.getData()) {
            //单个添加crm角色，返回错误信息后，认为之后的人员赋予角色都是失败的，直接return
            Result<String> createRoleCodeResult = crmManager.createRoleCode(corpMappingVo.getEa(), corpMappingVo.getEi(), Lists.newArrayList(datum), roleCode);
            if(!createRoleCodeResult.isSuccess()) {
                log.info("add role count out of range ei :{},roleCode:{}",corpMappingVo.getEi(),roleCode);
                return;
            }
        }
        log.info("create auth user count :{}", empList.getData().size());
        return;
    }


    private void updateUser(DingMappingEmployeeResult employeeResult, Integer ei, Long appId, String dingCorpId,String suiteId) {
        EmployeeDingVo empVo = dingManager.getUserByUserId(dingCorpId, employeeResult.getDingEmployeeId(),suiteId);
        Result<Integer> result = cloudEmpService.cloudUpdateEmp(empVo, ei, appId, dingCorpId);
        log.info("updateUser managerResult:{}", result);
    }

    /**
     *  多应用的员工是所有的应用都没有权限才移除。但是部门不受限于多应用，只限制于权限范围变化以及部门增删改查。因为一开始如果是同步了全公司过来
     *  后面即使是授权范围变小，父级部门也没办法删除（因为crm不能删除正常有正常状态子级部门）所以部门就不做太多特殊处理
     * @param authData
     * @param suiteId
     */
    public void updateScopeRole(AuthEnterPriseModel.BizData authData,String suiteId,Integer ei) {
        //查询最新范围内的员工

        AuthEnterPriseModel.AuthOrgScopes authOrgScopes = authData.getAuthScope().getAuthOrgScopes();
        String dingCorpId = authData.getAuthCorpInfo().getCorpId();
        Long appId = authData.getAuthInfo().getAgent().get(0).getAppId();
        Set<String> scopeEmpSet = Sets.newHashSet();
        //查询所在权限下的所有部门
        List<Dept> scopeDepts = null;
        if(CollectionUtils.isNotEmpty(authOrgScopes.getAuthedDept())){
            scopeDepts = dingManager.querySimpleDeptByScope(authOrgScopes.getAuthedDept(), dingCorpId,suiteId);
            scopeDepts.stream().forEach(item -> {
                //TODO 添加部门
                //查询部门员工信息
                EmployeeDingVo dingDeptOwner = dingManager.getUserByUserId(dingCorpId, item.getDeptOwner(), suiteId);
                needAddDept(Lists.newArrayList(item), dingCorpId, ei, suiteId, dingDeptOwner, String.valueOf(appId));
                List<EmployeeDingVo> employeeDingVoList = dingManager.queryUserByDept(authData.getAuthCorpInfo().getCorpId(), item.getId(),suiteId);
                scopeEmpSet.addAll(employeeDingVoList.stream().map(EmployeeDingVo::getUserid).collect(Collectors.toSet()));
            });
        }
        scopeEmpSet.addAll(authData.getAuthScope().getAuthOrgScopes().getAuthedUser());

        //查询user_app表，查询之前该应用授权范围内映射的员工
        Result<List<UserAppResult>> preUserMapping = userAppMappingService.queryMappingByAppId(dingCorpId, appId);
        Set<String> preUserSet = preUserMapping.getData().stream().map(UserAppResult::getDingEmpId).collect(Collectors.toSet());
        //scope-preUser差集求出需要新增员工的授权范围
        List<String> addEmpList = scopeEmpSet.stream().filter(item -> !preUserSet.contains(item)).collect(Collectors.toList());
        needAddEmp(addEmpList, dingCorpId, appId,suiteId);

        //此时部门和部门负责人都创建成功，只需要修改部门
        if(CollectionUtils.isNotEmpty(scopeDepts)) {
            log.info("EnterpriseAuthBizType4ServiceImpl.updateScopeRole,create deptOwner,scopeDepts={}.", scopeDepts);
            scopeDepts.stream().forEach(item -> needModifyDept(Lists.newArrayList(item),dingCorpId,ei,suiteId, null, appId));
        }

        //preUser-scope需要移除该授权范围的员工
        List<String> needRemoveList = preUserSet.stream().filter(item -> !scopeEmpSet.contains(item)).collect(Collectors.toList());
        needRemoveEmp(needRemoveList, dingCorpId, appId);
    }

    public void needAddDept(List<Dept> depts, String dingCorpId, Integer ei, String suiteId, EmployeeDingVo employeeDingVo, String appId){
        for (Dept dept : depts) {
            Result<OuterOaDepartmentBindEntity> deptVoResult = cloudDeptService.queryDept(ei, dept.getId(), appId);
            if(ObjectUtils.isEmpty(deptVoResult.getData())){
                cloudDeptService.createDept(dept, dingCorpId,ei, appId, suiteId);
            }else{
                cloudDeptService.modifyDept(dept,dingCorpId,ei, appId, suiteId);
            }
        }
    }

    public void needModifyDept(List<Dept> depts, String dingCorpId, Integer ei, String suiteId, EmployeeDingVo employeeDingVo, Long appId){
        for (Dept dept : depts) {
            final String id = String.valueOf(appId);
            Result<OutDepartmentInfoDto> deptVoResult = cloudDeptService.queryDeptInfo(ei, dept.getId(), id);
            if(ObjectUtils.isNotEmpty(deptVoResult.getData()) && ObjectUtils.isNotEmpty(deptVoResult.getData().getOutDeptData()) && deptVoResult.getData().getOutDeptData().getOwner() == null && StringUtils.isNotEmpty(dept.getDeptOwner())){
                cloudDeptService.modifyDept(dept, dingCorpId, ei, id, suiteId);
            }
        }
    }

    public void needAddEmp(List<String> empList, String dingCorpId, Long appId,String suiteId) {
        log.info("trace add emp list:{}",empList);
        if (CollectionUtils.isEmpty(empList)) return;
        //查询ding_emp_mapping是否已经创建该员工
        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId(dingCorpId, appId);
        Integer ei = dingCorpMappingVoResult.getData().getEi();
        for (String empItem : empList) {
            Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmpByDingUserId(ei, empItem, String.valueOf(appId));
            Integer crmEmpId = null;
            List<EmployeeDingVo> authEmp = dingManager.getDingEmp(dingCorpId, Lists.newArrayList(empItem),suiteId);
            if (ObjectUtils.isEmpty(mappingResult.getData())) {
                //新增员工
                Result<Integer> createEmpResult = cloudEmpService.cloudCreateEmp(authEmp.get(0), ei, appId, dingCorpId);
                crmEmpId = createEmpResult.getData();
                if(createEmpResult.getErrorCode()==ResultCode.ENTERPRISE_COUNT_FULL.getErrorCode()){
                    //创建员工失败，则直接返回
                    log.info("bizType 4 add emp fail :{},result:{}",createEmpResult.getData(),authEmp);
                    return;
                }
            } else {
                //如果是状态是2（禁用）说明之前授权范围变更时候停用了，重启员工
                log.info("trace active authEmp list:{}",authEmp);
                crmEmpId = mappingResult.getData().getEmployeeId();
                if(organizationService.checkEmployeeStop(crmEmpId, ei)){
                    cloudEmpService.cloudUpdateEmp(authEmp.get(0),ei,appId,dingCorpId);
                }
            }
            //添加员工角色
            String enterpriseAccount = eieaConverter.enterpriseIdToAccount(ei);
            String roleCode = Optional.ofNullable(ConfigCenter.ROLE_MAP.get(appId)).orElseGet(() -> "00000000000000000000000000000015");
            if(!ConfigCenter.NOT_SYNC_ROLE.contains(String.valueOf(ei))){//过滤不需要同步的企业
                Result<String> createResult = crmManager.createRoleCode(enterpriseAccount, ei, Lists.newArrayList(crmEmpId), roleCode);
                log.info("trace active authEmp get0 list:{}",authEmp);
                if(authEmp.get(0).getAdmin()){
                    //查询一下。如果该员工是管理员，则添加上管理员角色
                    log.info("trace addManagerRole authEmp get0 list:{}",authEmp);
                    crmManager.addManagerRole(ei,crmEmpId);
                }
                log.info("bizType4 create result:{}",createResult);
            }

            //插入usr_app表
            UserMappingVo userMappingArg = UserMappingVo.builder().ei(ei).appId(appId).dingCorpId(dingCorpId).crmEmpId(crmEmpId).dingEmpId(empItem).build();
            Result<Integer> result = userAppMappingService.insertDingEmp(userMappingArg);
            log.info("bizType4 insertDingEmp result:{}",result);
        }
    }

    public void needRemoveEmp(List<String> empList, String dingCorpId, Long appId) {
        //查询ding_emp_mapping是否已经创建该员工
        if (CollectionUtils.isEmpty(empList)) return;
        Result<DingCorpMappingVo> dingCorpMappingVoResult = dingCorpMappingService.queryMappingByAppId(dingCorpId, appId);
        Integer ei = dingCorpMappingVoResult.getData().getEi();
        String enterpriseAccount = eieaConverter.enterpriseIdToAccount(ei);
        for (String empItem : empList) {
            Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmpByDingUserId(ei, empItem, String.valueOf(appId));
            if (ObjectUtils.isEmpty(mappingResult.getData())) continue;
            Integer crmEmpId = mappingResult.getData().getEmployeeId();
            //添加员工角色
            Result<String> roleCode = crmManager.removeRoleCode(enterpriseAccount, ei, Lists.newArrayList(crmEmpId), appId);
            log.info("remove app ei:{},role:{}", ei, crmEmpId);
            //如果该移除员工都不在该企业所授权的应用下，需要把该员工停用
            // 查询该员工有没有在其他应用中
            Result<Integer> forbidResult = cloudEmpService.batchForbidEmp(empItem, ei, dingCorpId, appId);
            log.info("update scope forbid item:{},result:{}", empItem, forbidResult);
        }
    }

    /**
     * 同步授权范围变化的部门
     *
     */
    public void updateScopeDept(AuthEnterPriseModel.BizData authData,String suiteId){
        //查询最新范围的部门
    }




    /**
     * 生成自定义ea
     */
    public String createCustomEnterpriseAccount(String enterpriseName) {
        String ea = ENTERPRISE_PREFIX + PinyinUtils.converterToFirstSpell(enterpriseName);

        return StringUtils.substring(ea, 0, 20);
    }

    /**
     * 试用订单
     */
    public Result<String> createFreeOrder(String data, String enterpriseAccount, Integer maxPeople) {
        //创建订单 bizType=63
        //试用订单没有订单的id
        FreeOrderModel freeOrderModel = JSONObject.parseObject(data, new TypeReference<FreeOrderModel>() {
        });
        freeOrderModel.setCorpId("dingcc7ead824f9fa7e0a39a90f97fcb1e09");
        freeOrderModel.setGoodsCode("mock");
        freeOrderModel.setTimeStamp("*************");
        freeOrderModel.setFromDate("2021-05-10 09:45:53");
        freeOrderModel.setEndDate("2021-07-10 09:45:53");

        String crmProductId = ObjectUtils.isEmpty(ConfigCenter.GOODS_CODE.get(freeOrderModel.getGoodsCode())) ? ConfigCenter.TEMP_CODE : ConfigCenter.GOODS_CODE.get(freeOrderModel.getGoodsCode());
        CreateCrmOrderArg createCrmOrderArg = new CreateCrmOrderArg();
//        //todo 钉钉的订单与纷享的订单匹配
        CreateCrmOrderArg.CrmOrderDetailInfo crmOrderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        crmOrderDetailInfo.setOrderId(UUID.randomUUID().toString().replace("-", ""));
        crmOrderDetailInfo.setEnterpriseAccount(enterpriseAccount);
        crmOrderDetailInfo.setOrderTpye(ConfigCenter.ORDER_TRY_TYPE);
        crmOrderDetailInfo.setOrderTime(Long.parseLong(freeOrderModel.getTimeStamp()));
        createCrmOrderArg.setCrmOrderDetailInfo(crmOrderDetailInfo);
        CreateCrmOrderArg.CrmOrderProductInfo crmOrderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        crmOrderProductInfo.setProductId("609d087534a5d700013d4027");
        crmOrderProductInfo.setQuantity(maxPeople);
        crmOrderProductInfo.setAllResourceCount(maxPeople);
        crmOrderProductInfo.setOrderAmount("0");
        try {
            Date fromDate = DateUtils.parseDate(freeOrderModel.getFromDate(), new String[]{"yyyy-MM-dd HH:mm:ss"});
            Date endDate = DateUtils.parseDate(freeOrderModel.getEndDate(), new String[]{"yyyy-MM-dd HH:mm:ss"});
            crmOrderProductInfo.setBeginTime(fromDate.getTime());
            crmOrderProductInfo.setEndTime(endDate.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        createCrmOrderArg.setCrmOrderProductInfo(crmOrderProductInfo);
        Result<String> orderResult = crmManager.createCrmOrder(createCrmOrderArg);
        return orderResult;
    }


}
