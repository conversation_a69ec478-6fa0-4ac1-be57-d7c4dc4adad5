package com.facishare.open.huawei.kit.web.result;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum ResultCodeEnum {
    /**
     * 成功
     */
    SUCCESS("000000", "Success"),

    /**
     * 鉴权失败
     */
    INVALID_TOKEN("000001", "token is invalid"),

    /**
     * 请求参数不合法
     */
    INVALID_PARAM("000002", "param is invalid"),

    /**
     * 实例ID不存在（商品续费、过期、资源释放以及升配需要校验）
     */
    INVALID_INSTANCE("000003", "instance is invalid"),

    /**
     * 请求处理中（特殊场景使用，普通ISV请忽略）
     */
    IN_PROCESS("000004", "request is processing"),

    /**
     * 其他内部错误
     */
    OTHER_INNER_ERROR("000005", "other inner error"),

    /**
     * 无可用实例资源分配（新购或升配如果没有可用资源时返回）
     */
    NO_AVAILABLE_RESOURCE("000100", "no available resource"),

    /**
     * 手机号为空或重复
     */
    PHONE_NUMBER_PROBLEM("000101", "Mobile number not specified or already exists"),

    /**
     * 邮箱号为空或重复
     */
    EMAIL_ADDRESS_PROBLEM("000102", "Email address is not specified or already exists"),

    /**
     * 该商品不可重复购买
     */
    PURCHASED_PRODUCT("000103", "Product already purchased"),

    /**
     * 该商品内部服务异常
     */
    INTERNAL_SERVICE_ERROR("000104", "Internal service error"),

    /**
     * 该账号已购买权益，无法再开通
     */
    ALREADY_SUBSCRIBED("0000105", "Account has already subscribed to benefits"),

    /**
     * 该商品资源已售罄
     */
    SOLD_OUT("0000106", "Product resources sold out"),

    /**
     * 该SaaS邮箱域名重复
     */
    DUPLICATE_EMAIL("000107", "SaaS email domain already exists");

    /**
     * 错误码
     */
    private final String resultCode;
    /**
     * 错误信息
     */
    private final String resultMsg;

    ResultCodeEnum(String resultCode, String resultMsg) {
        this.resultCode = resultCode;
        this.resultMsg = resultMsg;
    }

    public String getResultCode() {
        return resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }
}

