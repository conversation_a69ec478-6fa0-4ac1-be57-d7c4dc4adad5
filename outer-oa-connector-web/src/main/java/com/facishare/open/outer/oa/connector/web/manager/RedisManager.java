package com.facishare.open.outer.oa.connector.web.manager;

import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.common.api.result.ResultCodeEnum;
import com.facishare.restful.common.StopWatch;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisManager {

    private static final Logger logger = LoggerFactory.getLogger(RedisManager.class);
    private static final String REFRESH_DATA_KEY = "OA_BASE_%s";
    // 设置锁的等待时间和持有时间
    private static final long LOCK_WAIT_TIME = 5;
    private static final long LOCK_LEASE_TIME = 30;
    private static final TimeUnit TIME_UNIT = TimeUnit.SECONDS;

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取数据刷新锁的key
     * 
     * @param dataCenterId 数据中心ID
     * @return 格式化后的Redis key
     */
    private String getRefreshDataKey(String dataCenterId) {
        return String.format(REFRESH_DATA_KEY, dataCenterId);
    }

    /**
     * 刷新用户数据，使用Redis分布式锁避免并发
     * 
     * @param dataCenterId                数据中心ID
     * @param channelEnum                 渠道枚举
     * @param outerAbstractSettingService 外部设置服务
     * @return 刷新结果
     */
    public Result<Void> refreshUserData(String dataCenterId, ChannelEnum channelEnum,
            OuterAbstractSettingService outerAbstractSettingService) {
        // 使用stopwatch计时
        log.info("refreshUserData:{}",dataCenterId);
        StopWatch refreshDataStopWatch = StopWatch.create("refreshUserData");

        // 获取分布式锁的键
        String lockKey = getRefreshDataKey(dataCenterId);
        // 获取分布式锁
        RLock lock = redissonClient.getLock(lockKey);
        Result result = null;

        try {
            // 尝试获取锁，最多等待LOCK_WAIT_TIME秒，锁自动释放时间为LOCK_LEASE_TIME秒
            boolean isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TIME_UNIT);
            refreshDataStopWatch.lap("acquire lock");
            if (isLocked) {
                try {
                    // 执行刷新操作
                    result = outerAbstractSettingService.refreshOuterEmpData(dataCenterId, channelEnum);
                    refreshDataStopWatch.lap("refresh data");
                } catch (Exception e) {
                    logger.error("Failed to refresh user data, dataCenterId: {}, channelEnum: {}", dataCenterId,
                            channelEnum, e);
                    result = Result.newError(ResultCodeEnum.SYSTEM_ERROR,
                            "Failed to refresh user data: " + e.getMessage());
                }
            } else {
                logger.warn(
                        "Failed to acquire lock, another process may be refreshing data, dataCenterId: {}, channelEnum: {}",
                        dataCenterId, channelEnum);
                result = Result.newError(ResultCodeEnum.SYSTEM_ERROR,
                        "Failed to acquire data refresh lock, please try again later");
            }
        } catch (Exception e) {
            // 统一处理所有异常
            logger.error("Error occurred during data refresh, dataCenterId: {}, channelEnum: {}", dataCenterId,
                    channelEnum, e);
            if (e instanceof InterruptedException) {
                Thread.currentThread().interrupt();
            }
            result = Result.newError(ResultCodeEnum.SYSTEM_ERROR, "Error during data refresh: " + e.getMessage());
        } finally {
            // 释放锁
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
                refreshDataStopWatch.lap("release lock");
            }
            // 记录执行耗时
            refreshDataStopWatch.log();
        }

        return result;
    }

    /**
     * 检查指定数据中心ID的锁是否可以获取（轻量级检查，不获取锁）
     * 
     * @param dataCenterId 数据中心ID
     * @return 如果Redis中不存在此key则返回true，否则返回false
     */
    public Boolean validRefreshUserDataLock(String dataCenterId) {
        try {
            // 获取分布式锁的键
            String lockKey = getRefreshDataKey(dataCenterId);

            // 直接检查Redis中是否存在该key，不尝试获取锁
            boolean exists = redissonClient.getKeys().countExists(lockKey) > 0;

            // 如果key存在，表示锁被占用
            if (exists) {
                logger.info("Lock key for dataCenterId: {} exists in Redis, cannot acquire lock", dataCenterId);
                return false;
            } else {
                logger.info("Lock key for dataCenterId: {} does not exist in Redis, lock is available", dataCenterId);
                return true;
            }
        } catch (Exception e) {
            logger.error("Error checking lock availability for dataCenterId: {}", dataCenterId, e);
           return true;
        }
    }
}
