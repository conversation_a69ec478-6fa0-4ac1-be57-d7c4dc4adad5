package com.facishare.open.order.contacts.proxy.api.network;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.limiter.SpeedLimiter;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.fxiaoke.common.http.handler.SyncCallback;
import com.fxiaoke.common.http.spring.OkHttpSupport;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/2/18
 **/
@Slf4j
@Component
public class ProxyHttpClient {
    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY = 5;
    
    /**
     * 重试间隔基数（毫秒）
     */
    private static final long RETRY_INTERVAL_BASE = 1000;

    @Resource(name = "okHttpSupport")
    private OkHttpSupport okHttpSupport;

    @Autowired
    private SpeedLimiter speedLimiter;
    
    /**
     * 判断是否为网络异常
     */
    private boolean isNetworkError(Exception e) {
        if(!ConfigCenter.allow_retry_network_error){
            log.info("envconfig allow retry false");
            return false;
        }
        return e instanceof IOException || 
               (e.getMessage() != null && (
                   e.getMessage().contains("Socket closed") ||
                   e.getMessage().contains("stream was reset") ||
                   e.getMessage().contains("Connection reset") ||
                   e.getMessage().contains("timeout")
               ));
    }

    private RequestBody createJsonBody(Object params, MediaType mediaType) {
        return params instanceof String
                ? RequestBody.create(params.toString(), ObjectUtils.isNotEmpty(mediaType) ? mediaType : JSON_TYPE)
                : RequestBody.create(JSON.toJSONString(params), ObjectUtils.isNotEmpty(mediaType) ? mediaType : JSON_TYPE);
    }

    public <T> T postUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlSerialNull(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSONObject.toJSONString(params);
        return postUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T postUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        
        // 增加重试逻辑
        T result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.parseObject(request, typeReference);
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Post URL failed, url={}, params={}, headerMap={}, error={}", url, paramsJson, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待，时间随重试次数增加
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Post URL failed after {} retries, url={}, params={}", retryCount, url, paramsJson);
        }
        
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}, retryCount={}", url, paramsJson, headerMap, result, retryCount);
        return result;
    }

    public <T> T deleteByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("deleteByJson, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).delete(requestBody).build();
        
        // 增加重试逻辑
        T result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.parseObject(request, typeReference);
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Delete URL failed, url={}, params={}, headerMap={}, error={}", url, paramsJson, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待，时间随重试次数增加
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Delete URL failed after {} retries, url={}, params={}", retryCount, url, paramsJson);
        }
        
        log.debug("deleteByJson result, url={}, params={}, headerMap={}, result={}, retryCount={}", url, paramsJson, headerMap, result, retryCount);
        return result;
    }

    public <T> T getUrl(String url, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
        }
        if(!isAllow) {
            return null;
        }
        
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        
        // 增加重试逻辑
        T result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.parseObject(request, typeReference);
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Get URL failed, url={}, headerMap={}, error={}", url, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Get URL failed after {} retries, url={}", retryCount, url);
        }
        
        log.debug("getUrl result, url={}, headerMap={}, result={}, retryCount={}", url, headerMap, result, retryCount);
        return result;
    }

    public String postUrl(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        
        RequestBody requestBody = this.createJsonBody(params, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        
        // 增加重试逻辑
        Object result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Post URL failed, url={}, params={}, headerMap={}, error={}", url, params, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待，时间随重试次数增加
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Post URL failed after {} retries, url={}, params={}", retryCount, url, params);
        }
        
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}, retryCount={}", url, params, headerMap, result, retryCount);
        return result != null ? result.toString() : null;
    }

    public String postUrlSerialNull(String url, Object params, Map<String, String> headerMap) {
        log.debug("postUrl, url={}, params={}, headerMap={}", url, params, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        
        RequestBody requestBody = params instanceof String
                ? RequestBody.create(JSON_TYPE, params.toString())
                : RequestBody.create(JSON_TYPE, JSONObject.toJSONString(params));
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).post(requestBody).build();
        
        // 增加重试逻辑
        Object result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Post URL failed, url={}, params={}, headerMap={}, error={}", url, params, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待，时间随重试次数增加
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Post URL failed after {} retries, url={}, params={}", retryCount, url, params);
        }
        
        log.debug("postUrl result, url={}, params={}, headerMap={}, result={}, retryCount={}", url, requestBody, headerMap, result, retryCount);
        return result != null ? result.toString() : null;
    }


    public String getUrl(String url, Map<String, String> headerMap) {
        log.debug("getUrl, url={}, headerMap={}", url, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
        }
        if(!isAllow) {
            return null;
        }
        
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).build();
        
        // 增加重试逻辑
        Object result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.syncExecute(request, new ProxySyncCallback());
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Get URL failed, url={}, headerMap={}, error={}", url, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Get URL failed after {} retries, url={}", retryCount, url);
        }
        
        log.debug("getUrl result, url={}, headerMap={}, result={}, retryCount={}", url, headerMap, result, retryCount);
        return result != null ? result.toString() : null;
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    public static class ProxySyncCallback extends SyncCallback {

        @Override
        public Object response(Response response) throws Exception {
            //获取响应头
            if(ObjectUtils.isNotEmpty(response) && ObjectUtils.isNotEmpty(response.headers())) {
                Headers responseHeaders = response.headers();
                int statusCode = response.code();
                log.info("ProxySyncCallback, status={}, response={}", statusCode, responseHeaders);
                
                // 记录关键响应头，有助于排查问题
                String requestId = responseHeaders.get("X-Request-Id");
                String traceId = responseHeaders.get("X-Trace-Id");
                if (StringUtils.isNotEmpty(requestId) || StringUtils.isNotEmpty(traceId)) {
                    log.info("Key response headers: X-Request-Id={}, X-Trace-Id={}", requestId, traceId);
                }
                
                // 检查是否有限流相关的响应头
                String rateLimit = responseHeaders.get("X-RateLimit-Limit");
                String rateLimitRemaining = responseHeaders.get("X-RateLimit-Remaining");
                if (StringUtils.isNotEmpty(rateLimit) || StringUtils.isNotEmpty(rateLimitRemaining)) {
                    log.info("Rate limit info: X-RateLimit-Limit={}, X-RateLimit-Remaining={}", rateLimit, rateLimitRemaining);
                }
            }
            
            return response.body() != null ? response.body().string() : "null";
        }
    }

    public <T> T patchUrl(String url, Object params, Map<String, String> headerMap, TypeReference<T> typeReference) {
        String paramsJson = params instanceof String ? params.toString() : JSON.toJSONString(params);
        return patchUrlByJson(url,paramsJson,headerMap,typeReference);
    }

    public <T> T patchUrlByJson(String url, String paramsJson, Map<String, String> headerMap, TypeReference<T> typeReference) {
        log.debug("patchUrlByJson, url={}, params={}, headerMap={}", url, paramsJson, headerMap);
        //限速
        boolean isAllow = Boolean.TRUE;
        MediaType mediaType = null;
        if(ObjectUtils.isNotEmpty(headerMap)) {
            if(headerMap.containsKey(GlobalValue.outLimit)
                    && headerMap.containsKey(GlobalValue.outLimitTimes)) {
                isAllow = speedLimiter.isAllow(headerMap.get(GlobalValue.outLimit), Long.valueOf(headerMap.get(GlobalValue.outLimitTimes)), StringUtils.isNotEmpty(headerMap.get(GlobalValue.outLimitSpeedSeconds)) ? Long.valueOf(headerMap.get(GlobalValue.outLimitSpeedSeconds)) : GlobalValue.LIMIT_SPEED_SECONDS, 3);
            }
            if(headerMap.containsKey(GlobalValue.contentType)) {
                mediaType = MediaType.parse(headerMap.get(GlobalValue.contentType));
            }
        }
        if(!isAllow) {
            return null;
        }
        
        RequestBody requestBody = this.createJsonBody(paramsJson, mediaType);
        Request request = (new Request.Builder()).url(url).headers(Headers.of(headerMap)).patch(requestBody).build();
        
        // 增加重试逻辑
        T result = null;
        int retryCount = 0;
        Exception lastException = null;
        
        while (retryCount <= MAX_RETRY) {
            try {
                // 执行请求
                result = this.okHttpSupport.parseObject(request, typeReference);
                // 请求成功，跳出循环
                break;
            } catch (Exception e) {
                lastException = e;
                // 判断是否是网络异常
                boolean isNetworkError = isNetworkError(e);
                
                // 如果是最后一次重试或者不是网络异常，记录错误并退出
                if (retryCount >= MAX_RETRY || !isNetworkError) {
                    log.error("Patch URL failed, url={}, params={}, headerMap={}, error={}", url, paramsJson, headerMap, e.getMessage(), e);
                    break;
                }
                
                // 网络异常，准备重试
                log.warn("Network error detected, preparing retry #{}, url={}, error={}", retryCount + 1, url, e.getMessage());
                
                // 重试前等待，时间随重试次数增加
                try {
                    long sleepTime = RETRY_INTERVAL_BASE * (retryCount + 1);
                    Thread.sleep(sleepTime);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("Retry wait interrupted");
                }
                
                // 增加重试计数
                retryCount++;
            }
        }
        
        if (result == null && lastException != null && retryCount > 0) {
            log.error("Patch URL failed after {} retries, url={}, params={}", retryCount, url, paramsJson);
        }
        
        log.debug("patchUrlByJson result, url={}, params={}, headerMap={}, result={}, retryCount={}", url, paramsJson, headerMap, result, retryCount);
        return result;
    }
}
