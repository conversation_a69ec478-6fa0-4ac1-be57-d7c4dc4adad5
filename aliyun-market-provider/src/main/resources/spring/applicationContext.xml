<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task" xmlns:c="http://www.springframework.org/schema/c"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.facishare.open.outer.oa.connector.common,com.facishare.open.aliyun.market"/>
    <task:annotation-driven executor="taskExecutor"/>
    <import resource="spring-cms.xml"/>
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="dubbo-config.xml"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="dubbo-consumer.xml"/>
    <import resource="spring-db.xml"/>
    <import resource="all-rest-api.xml"/>
    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
<!--    <import resource="spring-job.xml"/>-->
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>
    <!-- 引入license-->
    <import resource="classpath:spring/license-client.xml"/>
    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>
    <!--监控-->
    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!--redis配置-->
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-aliyun-market-config"/>
    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>

    <!--okHttp-->
    <bean id="okHttpSupport" class="com.fxiaoke.common.http.spring.HttpSupportFactoryBean"
          p:configName="fs-aliyun-market-config"/>


    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>


    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 蜂眼监控 -->
    <bean id="serviceProfiler" class="com.github.trace.aop.ServiceProfiler"/>
<!--    日志类-->
    <bean class="com.facishare.open.order.contacts.proxy.api.utils.LogUtils"/>
    <bean id="serviceAspect" class="com.facishare.open.order.contacts.proxy.api.aop.ServiceAspect"/>
    <aop:config>
        <aop:pointcut id="pointcut1" expression="(execution(* com.facishare.open.aliyun.market.service.impl.*.*(..)))
            || (execution(* com.facishare.open.aliyun.market.manager.*.*(..)))"/>
        <aop:aspect id="monitor" ref="serviceProfiler">
            <aop:around method="profile" pointcut-ref="pointcut1"/>
        </aop:aspect>
        <aop:aspect ref="serviceAspect">
            <aop:around method="around" pointcut-ref="pointcut1"/>
        </aop:aspect>
    </aop:config>


    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>

</beans>