package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_service_auth")
public class ServiceAuthEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 纷享服务名
     */
    private String fsService;
    /**
     * 服务key
     */
    private String fsKey;
    /**
     * 服务secret
     */
    private String fsSecret;
    /**
     * 状态
     */
    private Integer status;

    private Date createTime;
    private Date updateTime;
}
