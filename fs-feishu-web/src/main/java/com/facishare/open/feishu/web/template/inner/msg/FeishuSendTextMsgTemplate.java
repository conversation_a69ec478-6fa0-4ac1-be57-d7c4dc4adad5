package com.facishare.open.feishu.web.template.inner.msg;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.facishare.open.feishu.syncapi.arg.SendTextMessagePushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.OAMessageTag;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalMsgService;
import com.facishare.open.feishu.syncapi.service.ExternalTodoMsgService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 飞书发送消息模板
 * <AUTHOR>
 * @date 20241225
 */

@Slf4j
@Component
public class FeishuSendTextMsgTemplate extends SendMsgHandlerTemplate {
    @Resource
    private ExternalMsgService externalMsgService;
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;
    @Resource
    private EIEAConverter eieaConverter;

    @Override
    protected void filterMsg(MethodContext context) {
        log.info("FeishuSendTextMsgTemplate.filterMsg,context={}",context);
        SendTextMessagePushArg sendTextMessagePushArg = context.getData();
        String msgType = sendTextMessagePushArg.getMsgType();
        String upstreamEa = sendTextMessagePushArg.getUpstreamEa();
        OuterOaEnterpriseBindEntity enterpriseBindEntity = sendTextMessagePushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        Integer fsEi = eieaConverter.enterpriseAccountToId(fsEa);
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        SendTextMessageArg sendTextMessageArg = sendTextMessagePushArg.getSendTextMessageArg();

        Gson gson = new Gson();

        //互联待办需要配置才能使用
        if (msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
            if (StringUtils.isEmpty(upstreamEa)) {
                context.setResult(TemplateResult.newError(null));
                return;
            }
            Map<String, String> interconnectSecretKeyMap = gson.fromJson(ConfigCenter.INTERCONNECT_TEM_SECRET_KEY, new TypeToken<Map<String, String>>() {
            });

            if (!interconnectSecretKeyMap.containsKey(upstreamEa)) {
                context.setResult(TemplateResult.newError(null));
                return;
            }

            //只有配置了才能继续往下走
        }

        FunctionMsgBase functionMsgBase = new FunctionMsgBase();
        functionMsgBase.setChannel(enterpriseBindEntity.getChannel().name());
        functionMsgBase.setFsEa(fsEa);
        functionMsgBase.setOutEa(outEa);
        functionMsgBase.setAppId(appId);
        functionMsgBase.setType(FunctionMsgTypeEnum.crmExternalMsgPush.getType());
        functionMsgBase.setEventType(OAMessageTag.TEXT_MSG_TAG);
        functionMsgBase.setData(gson.toJson(sendTextMessageArg));

        // todo:函数过滤，先放在配置中心吧
        Map<String, String> filterCalendarEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });

        //根据函数判断是否继续
        if (filterCalendarEaMap.containsKey(fsEa) && StringUtils.isNotEmpty(filterCalendarEaMap.get(fsEa))) {
            Boolean isPull = crmMessagePullLimiter.messageIsPull(fsEi, filterCalendarEaMap.get(fsEa), functionMsgBase);
            if (!isPull) {
                context.setResult(TemplateResult.newError(null));
                return;
            }
        }

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void buildMsg(MethodContext context) {
        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void sendMsg(MethodContext context) {
        log.info("FeishuSendTextMsgTemplate.sendMsg,context={}",context);
        SendTextMessagePushArg sendTextMessagePushArg = context.getData();
        externalMsgService.sendTextMessage(sendTextMessagePushArg);
        context.setResult(TemplateResult.newSuccess());
    }
}
