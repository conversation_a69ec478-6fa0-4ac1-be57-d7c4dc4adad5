package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.FileMapper;
import com.facishare.open.feishu.syncapi.entity.FileEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class FileTableManager {
    @Resource
    private FileMapper fileMapper;

    public Integer insert(FileEntity entity) {
        int count = fileMapper.insert(entity);
        LogUtils.info("FileManager.insert,count={}",count);
        return count;
    }

    public Integer update(FileEntity entity) {
        int count = fileMapper.updateById(entity);
        LogUtils.info("FileManager.update,count={}",count);
        return count;
    }

    public List<FileEntity> queryEntities(String fsEa, String npath, String mediaId) {
        LambdaQueryWrapper<FileEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileEntity::getFsEa, fsEa);
        if(StringUtils.isNotEmpty(npath)) {
            wrapper.eq(FileEntity::getNpath, npath);
        }
        if(StringUtils.isNotEmpty(mediaId)) {
            wrapper.eq(FileEntity::getMediaId, mediaId);
        }
        return fileMapper.selectList(wrapper);
    }
}
