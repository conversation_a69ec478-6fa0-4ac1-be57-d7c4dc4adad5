package com.facishare.open.feishu.web.filter;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.web.utils.DeepCopyUtils;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsObjServiceProxy;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

@Component
public class EnterpriseInterconnectFilter {

    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private FsObjServiceProxy fsObjServiceProxy;

    // 通用过滤方法
    public <T extends Serializable> List<T> filterArgs(T arg) {
        List<Integer> receiverIds = getReceiverIds(arg);
        if (CollectionUtils.isEmpty(receiverIds)) {
            return new LinkedList<>();
        }

        Map<String, T> argMap = new HashMap<>();
        for (Integer receiverId : receiverIds) {
            if (isNormalPush(receiverId)) {
                addReceiverIdToNormalPush(argMap, arg, receiverId);
            } else {
                InterconnectPushResult result;
                try {
                    result = handleInterconnectPush(arg, receiverId);
                } catch (Exception e) {
                    result = null;
                }
                if (result == null) {
                    continue;
                }
                addReceiverIdToInterconnectPush(argMap, arg, result);
            }
        }
        return new ArrayList<>(argMap.values());
    }

    // 从参数对象中获取接收者 ID 列表
    private List<Integer> getReceiverIds(Object arg) {
        try {
            if (arg instanceof CreateTodoArg) {
                return ((CreateTodoArg) arg).getReceiverIds();
            } else if (arg instanceof DealTodoArg) {
                return ((DealTodoArg) arg).getOperators();
            } else if (arg instanceof DeleteTodoArg) {
                return ((DeleteTodoArg) arg).getDeleteEmployeeIds();
            } else if (arg instanceof SendTextCardMessageArg) {
                return ((SendTextCardMessageArg) arg).getReceiverIds();
            } else if (arg instanceof SendTextMessageArg) {
                return ((SendTextMessageArg) arg).getReceiverIds();
            }
        } catch (Exception e) {
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    // 在参数对象中设置接收者 ID 列表
    private void setReceiverIds(Object arg, List<Integer> receiverIds) {
        try {
            if (arg instanceof CreateTodoArg) {
                ((CreateTodoArg) arg).setReceiverIds(receiverIds);
            } else if (arg instanceof DealTodoArg) {
                ((DealTodoArg) arg).setOperators(receiverIds);
            } else if (arg instanceof DeleteTodoArg) {
                ((DeleteTodoArg) arg).setDeleteEmployeeIds(receiverIds);
            } else if (arg instanceof SendTextCardMessageArg) {
                ((SendTextCardMessageArg) arg).setReceiverIds(receiverIds);
            } else if (arg instanceof SendTextMessageArg) {
                ((SendTextMessageArg) arg).setReceiverIds(receiverIds);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set receiver IDs", e);
        }
    }

    // 判断接收者 ID 是否为正常推送
    private boolean isNormalPush(Integer receiverId) {
        return receiverId != null && receiverId < 100000000;
    }

    // 处理互联推送并返回结果
    private InterconnectPushResult handleInterconnectPush(Object arg, Integer receiverId) {
        String empParam = createEmployeeParam(receiverId);
        Result<List<ObjectData>> queryEmployeeInterConnectResult = fsObjServiceProxy.queryEmployeeInterconnect(getEi(arg), empParam);
        if (!queryEmployeeInterConnectResult.isSuccess() || CollectionUtils.isEmpty(queryEmployeeInterConnectResult.getData())) {
            return null;
        }

        String outerTenantId = String.valueOf(queryEmployeeInterConnectResult.getData().get(0).get("outer_tenant_id"));
        Integer employeeId = Integer.valueOf(queryEmployeeInterConnectResult.getData().get(0).get("employee_id").toString());

        String entParam = createEnterpriseParam(outerTenantId);
        Result<List<ObjectData>> queryEnterpriseInterConnectResult = fsObjServiceProxy.queryEnterpriseInterconnect(getEi(arg), entParam);
        if (!queryEnterpriseInterConnectResult.isSuccess() || CollectionUtils.isEmpty(queryEnterpriseInterConnectResult.getData())) {
            return null;
        }

        String enterpriseAccount = String.valueOf(queryEnterpriseInterConnectResult.getData().get(0).get("enterprise_account"));
        int ei = eieaConverter.enterpriseAccountToId(enterpriseAccount);

        return new InterconnectPushResult(enterpriseAccount, employeeId, ei);
    }

    // 获取参数对象中的 `ei` 属性
    private int getEi(Object arg) {
        try {
             if (arg instanceof CreateTodoArg) {
                return ((CreateTodoArg) arg).getEi();
            } else if (arg instanceof DealTodoArg) {
                return ((DealTodoArg) arg).getEi();
            } else if (arg instanceof DeleteTodoArg) {
                return ((DeleteTodoArg) arg).getEi();
            } else if (arg instanceof SendTextCardMessageArg) {
                return ((SendTextCardMessageArg) arg).getEi();
            } else if (arg instanceof SendTextMessageArg) {
                 return ((SendTextMessageArg) arg).getEi();
             }
        } catch (Exception e) {
            throw new RuntimeException("Failed to get ei", e);
        }
        return 0;
    }

    private void setInterconnectArgs(Object arg, InterconnectPushResult result) {
        try {
            if (arg instanceof CreateTodoArg) {
                ((CreateTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((CreateTodoArg) arg).setEi(result.getEi());
                ((CreateTodoArg) arg).setReceiverIds(Lists.newArrayList(result.getEmployeeId()));
            } else if (arg instanceof DealTodoArg) {
                ((DealTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((DealTodoArg) arg).setEi(result.getEi());
                ((DealTodoArg) arg).setOperators(Lists.newArrayList(result.getEmployeeId()));
            } else if (arg instanceof DeleteTodoArg) {
                ((DeleteTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((DeleteTodoArg) arg).setEi(result.getEi());
                ((DeleteTodoArg) arg).setDeleteEmployeeIds(Lists.newArrayList(result.getEmployeeId()));
            } else if (arg instanceof SendTextCardMessageArg) {
                ((SendTextCardMessageArg) arg).setEa(result.getEnterpriseAccount());
                ((SendTextCardMessageArg) arg).setEi(result.getEi());
                ((SendTextCardMessageArg) arg).setReceiverIds(Lists.newArrayList(result.getEmployeeId()));
            } else if (arg instanceof SendTextMessageArg) {
                ((SendTextMessageArg) arg).setEa(result.getEnterpriseAccount());
                ((SendTextMessageArg) arg).setEi(result.getEi());
                ((SendTextMessageArg) arg).setReceiverIds(Lists.newArrayList(result.getEmployeeId()));
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to update interconnect args", e);
        }
    }

    // 更新参数对象中的互联推送信息
    private void updateInterconnectArgs(Object arg, InterconnectPushResult result) {
        try {
            if (arg instanceof CreateTodoArg) {
                ((CreateTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((CreateTodoArg) arg).setEi(result.getEi());
                ((CreateTodoArg) arg).getReceiverIds().add(result.getEmployeeId());
            } else if (arg instanceof DealTodoArg) {
                ((DealTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((DealTodoArg) arg).setEi(result.getEi());
                ((DealTodoArg) arg).getOperators().add(result.getEmployeeId());
            } else if (arg instanceof DeleteTodoArg) {
                ((DeleteTodoArg) arg).setEa(result.getEnterpriseAccount());
                ((DeleteTodoArg) arg).setEi(result.getEi());
                ((DeleteTodoArg) arg).getDeleteEmployeeIds().add(result.getEmployeeId());
            } else if (arg instanceof SendTextCardMessageArg) {
                ((SendTextCardMessageArg) arg).setEa(result.getEnterpriseAccount());
                ((SendTextCardMessageArg) arg).setEi(result.getEi());
                ((SendTextCardMessageArg) arg).getReceiverIds().add(result.getEmployeeId());
            } else if (arg instanceof SendTextMessageArg) {
                ((SendTextMessageArg) arg).setEa(result.getEnterpriseAccount());
                ((SendTextMessageArg) arg).setEi(result.getEi());
                ((SendTextMessageArg) arg).getReceiverIds().add(result.getEmployeeId());
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to update interconnect args", e);
        }
    }

    // 处理正常推送
    private <T extends Serializable> void addReceiverIdToNormalPush(Map<String, T> argMap, T arg, Integer receiverId) {
        try {
            String ea = (String) arg.getClass().getMethod("getEa").invoke(arg);
            if (!argMap.containsKey(ea)) {
                T normalArg = DeepCopyUtils.deepCopy(arg);
                setReceiverIds(normalArg, new LinkedList<>(Lists.newArrayList(receiverId)));
                argMap.put(ea, normalArg);
            } else {
                List<Integer> existingReceiverIds = getReceiverIds(argMap.get(ea));
                existingReceiverIds.add(receiverId);
                setReceiverIds(argMap.get(ea), existingReceiverIds);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to add receiver ID to normal push", e);
        }
    }

    // 处理互联推送
    private <T extends Serializable> void addReceiverIdToInterconnectPush(Map<String, T> argMap, T arg, InterconnectPushResult result) {
        try {
            String enterpriseAccount = result.getEnterpriseAccount();
            if (!argMap.containsKey(enterpriseAccount)) {
                T interconnectArg =DeepCopyUtils.deepCopy(arg);
                setInterconnectArgs(interconnectArg, result);
                argMap.put(enterpriseAccount, interconnectArg);
            } else {
                updateInterconnectArgs(argMap.get(enterpriseAccount), result);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to add receiver ID to interconnect push", e);
        }
    }

    // 查询互联员工参数
    private String createEmployeeParam(Integer receiverId) {
        return "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"object_describe_api_name\":\"PublicEmployeeObj\",\"search_template_id\":\"" + ConfigCenter.INTERCONNECT_EMPLOYEE_TEMPLATE_ID + "\",\"include_describe\":false,\"include_layout\":false,\"need_tag\":true,\"search_template_type\":\"default\",\"ignore_scene_record_type\":true,\"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[{\\\"field_name\\\":\\\"_id\\\",\\\"field_values\\\":[\\\"" + receiverId + "\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100]}";
    }

    // 查询互联企业参数
    private String createEnterpriseParam(String outerTenantId) {
        return "{\"serializeEmpty\":false,\"extractExtendInfo\":true,\"object_describe_api_name\":\"EnterpriseRelationObj\",\"search_template_id\":\"" + ConfigCenter.INTERCONNECT_ENTERPRISE_TEMPLATE_ID + "\",\"include_describe\":false,\"include_layout\":false,\"need_tag\":true,\"search_template_type\":\"default\",\"ignore_scene_record_type\":true,\"search_query_info\":\"{\\\"limit\\\":20,\\\"offset\\\":0,\\\"filters\\\":[{\\\"field_name\\\":\\\"_id\\\",\\\"field_values\\\":[\\\"" + outerTenantId + "\\\"],\\\"operator\\\":\\\"EQ\\\"}],\\\"orders\\\":[{\\\"fieldName\\\":\\\"last_modified_time\\\",\\\"isAsc\\\":false}]}\",\"pageSizeOption\":[20,50,100]}";
    }

    @Data
    @AllArgsConstructor
    public static class InterconnectPushResult {
        private String enterpriseAccount;
        private Integer employeeId;
        private int ei;
    }
}
