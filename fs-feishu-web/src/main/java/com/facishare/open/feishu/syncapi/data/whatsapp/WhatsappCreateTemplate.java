package com.facishare.open.feishu.syncapi.data.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WhatsappCreateTemplate extends FsBase implements Serializable {
    /**
     * 牛信云WhatsApp应用的appkey
     */
    private String appkey;
    /**
     * 发送消息的通道，应用于WhatsApp消息的发送时，值必须为“whatsapp”
     */
    private String messaging_product = ChannelEnum.whatsapp.toString();
    /**
     * 商户的WhatsApp号码列表，需要带国码,如185xxx99
     */
    private String business_phone;
    /**
     * 模板类型
     * 枚举值:
     * "MARKETING" : "包括促销或优惠、信息更新或邀请客户回应/采取行动的信息。任何不符合效用或身份验证用途均为营销",
     * "UTILITY" : "促成商定的某个具体请求或交易，或向客户提供与进行中的交易相关的最新信息，包括售后通知和定期账单",
     * "AUTHENTICATION" ："使商家可以使用一次性密码验证用户身份，可能会在登录过程的多个步骤中出现（如帐号认证、帐号找回、诚信挑战）"
     */
    private String category;
    /**
     * 语言编码，例如zh_CN
     */
    private String language;
    /**
     * 模板名称
     * 只允许输入小写字母，数字，下划线
     */
    private String name;

    /**
     * 模板组件
     */
    private List<Component> components;
}
