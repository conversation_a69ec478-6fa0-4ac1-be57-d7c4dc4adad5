package com.facishare.open.feishu.syncapi.model.externalApprovals;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExternalApprovalsTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    @JSO<PERSON>ield(name = "approval_name")
    private String approvalName;

    @JSONField(name = "approval_code")
    private String approvalCode;

    @JSONField(name = "group_code")
    private String groupCode;

    @JSONField(name = "group_name")
    private String groupName;

    private String description;

    private External external;

    @JSONField(name = "i18n_resources")
    private List<I18nResource> i18nResources;

    private List<Viewer> viewers;

    private List<String> managers;

    @Data
    public static class External implements Serializable{
        @JSONField(name = "biz_name")
        private String bizName;

        @JSONField(name = "create_link_pc")
        private String createLinkPc;

        @J<PERSON>NField(name = "create_link_mobile")
        private String createLinkMobile;

        @JSONField(name = "support_pc")
        private Boolean supportPc;

        @JSO<PERSON>ield(name = "support_mobile")
        private Boolean supportMobile;

        @JSO<PERSON>ield(name = "support_batch_read")
        private Boolean supportBatchRead;

        @JSONField(name = "action_callback_url")
        private String actionCallbackUrl;

        @JSONField(name = "action_callback_token")
        private String actionCallbackToken;

        @JSONField(name = "action_callback_key")
        private String actionCallbackKey;

        @JSONField(name = "enable_mark_readed")
        private Boolean enableMarkReaded;
    }

    @Data
    public static class I18nResource implements Serializable{
        private String locale;

        @JSONField(name = "is_default")
        private Boolean isDefault;

        private List<Text> texts;

        @Data
        public static class Text implements Serializable{
            private String key;
            private String value;

        }
    }

    @Data
    public static class Viewer implements Serializable{
        @JSONField(name = "viewer_type")
        private String viewerType;
    }
}
