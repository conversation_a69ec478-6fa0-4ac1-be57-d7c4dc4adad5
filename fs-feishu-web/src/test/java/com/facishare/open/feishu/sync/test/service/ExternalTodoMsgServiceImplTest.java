package com.facishare.open.feishu.sync.test.service;

import com.facishare.open.feishu.sync.manager.EnterpriseBindManager;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.service.impl.ExternalTodoMsgServiceImpl;
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg;
import com.facishare.open.feishu.syncapi.arg.DealTodoPushArg;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.OutOaMsgEventTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class ExternalTodoMsgServiceImplTest extends BaseTest {
    @Autowired
    private ExternalTodoMsgServiceImpl externalTodoMsgService;
    @Autowired
    private OuterOaEnterpriseBindManager enterpriseBindManager;

    @Test
    public void testCreateTodo() {
        CreateTodoArg arg = new CreateTodoArg();
        arg.setGenerateUrlType(1);
        arg.setEa("91449");
        arg.setEi(91449);
        arg.setBizType("452");
        arg.setReceiverIds(Lists.newArrayList(1201,1000));
        arg.setContent("客户审批(2025-04-19 16:12");
        arg.setTitle("待处理的业务流程");
        List<KeyValueItem> form = new LinkedList<>();
        KeyValueItem valueItem = new KeyValueItem("公司名称", "字节");
        KeyValueItem valueItem1 = new KeyValueItem("啊行吧", "啊对吧");
        form.add(valueItem);
        form.add(valueItem1);
        arg.setForm(form);
        arg.setUrl("bpm?workflowInstanceId=65d317c7c26b8d323e19a184&ea=88521");
        arg.setSourceId("6672cc294bc2c77fb023c832");
        Map<String, String> extraDataMap = new HashMap<>();
        extraDataMap.put("workflowInstanceId", "65d317c7c26b8d323e19a184");
        extraDataMap.put("activityId", "*************");
        extraDataMap.put("objectApiName", "AccountObj");
        extraDataMap.put("objectId", "65d317a67622c400071004cd");
        arg.setExtraDataMap(extraDataMap);
        CreateTodoPushArg createTodoPushArg = new CreateTodoPushArg();
        createTodoPushArg.setMsgType(OutOaMsgEventTypeEnum.commonMsg.name());
        //查询所有绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = enterpriseBindManager.getEntitiesByFsEa(ChannelEnum.feishu,arg.getEa());
        for(OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            createTodoPushArg.setEnterpriseBindEntity(enterpriseBindEntity);
            createTodoPushArg.setCreateTodoArg(arg);
            externalTodoMsgService.createTodo(createTodoPushArg);
        }
    }

    @Test
    public void testDeal(){
        /**
         * FeishuDealTodoTemplate.sendMsg,context=MethodContext(data=DealTodoPushArg(msgType=commonMsg, upstreamEa=null, enterpriseBindEntity=OuterOaEnterpriseBindEntity(id=67f5ce157e1ac00001a4ae90, channel=feishu, fsEa=91449, outEa=100d08b69448975d, appId=cli_a3ddeb52763b100c,
         * connectInfo={"alertConfig":true,"alertTypes":["CRM_TODO","CRM_NOTIFICATION"]
         * ,"appId":"cli_a3ddeb52763b100c","appSecret":***","appType":"isv","channel":"feishu","connectorName":"飞书","corpId":"100d08b69448975d","dataCenterId":"67f5ce157e1ac00001a4ae90","dataCenterName":"isv测试","displayId":"F850420834","enterpriseName":"飞书CRM手动绑定企业"}, bindType=manual, bindStatus=normal, createTime=1744162325147, updateTime=1744875413254),
         * dealTodoArg=DealTodoArg(super=BaseExternalArg(generateUrlType=0, extraDataMap={}, groupKeys=[], appId=null), ea=91449, ei=91449, operators=[1201, 1000], sourceId=68038269780c807be70d82ef, bizType=452, handleUserIds=[1000], taskStatusEnum=null, extDataMap={opinions=[{"reply_user":["1000"],"feedId":"100000015","action_type":"
         * agree","reply_time":1745060524840,"id":"680382ac780c807be70d82f0","opinion":""}], state=pass})), result=TemplateResult(code=0, msg=null, data=null))
         */
        DealTodoPushArg dealTodoPushArg=new DealTodoPushArg();
        DealTodoArg dealTodoArg=new DealTodoArg();
        dealTodoArg.setEa("91449");
        dealTodoArg.setEi(91449);
        dealTodoArg.setOperators(Lists.newArrayList(1201,1000));
        dealTodoArg.setSourceId("68038269780c807be70d82ef");
        dealTodoArg.setBizType("452");
        dealTodoPushArg.setDealTodoArg(dealTodoArg);
        OuterOaEnterpriseBindEntity entityById = enterpriseBindManager.getEntityById("67f5ce157e1ac00001a4ae91");
        dealTodoPushArg.setEnterpriseBindEntity(entityById);
        externalTodoMsgService.dealTodo(dealTodoPushArg);
    }
}
