package com.facishare.open.feishu.sync.service

import com.facishare.open.feishu.syncapi.service.EnterpriseBindService
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import javax.annotation.Resource

@ContextConfiguration(locations = "classpath:spring-test/applicationContext-test.xml")
class EnterpriseBindServiceTest extends Specification {
    @Resource
    private EnterpriseBindService enterpriseBindService;

    def "autoBindEmpByFeishuEmployeeNumber"() {
        expect:
        def a = enterpriseBindService.fsBindWithFeishu("csazsy0973", "富贵", "F850420834")
        print(a)
    }

    def "cloudFsBindWithFeishu"() {
        expect:
        def a = enterpriseBindService.cloudFsBindWithFeishu("100d08b69448975d", "90562", "https://ale.ceshi112.com")
        print(a)
        where:
        type  |  outEa  |  fsEa  |  domain
        "add"  |  "100d08b69448975d"  |  "90561"  |  "https://ale.ceshi112.com"
        "update"  |  "100d08b69448975d"  |  "90562"  |  "https://ale.ceshi112.com"
    }
}
