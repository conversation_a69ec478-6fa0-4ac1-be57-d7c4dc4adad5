package com.facishare.open.feishu.sync.test.limiter;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.sync.limiter.CrmMessagePullLimiter;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.gson.Gson;
import org.junit.Test;

import javax.annotation.Resource;

public class CrmMessagePullLimiterTest extends BaseTest {
    @Resource
    private CrmMessagePullLimiter crmMessagePullLimiter;
    @Resource
    private EIEAConverter eieaConverter;

    @Test
    public void executeCustomFunction() {
        SendTextMessageArg arg = new Gson().fromJson("{\"ea\":\"fsbcsh3382\",\"ei\":12,\"senderId\":0,\"receiverIds\":[1000,1002,1003],\"messageContent\":\"【转移】执行完毕， 执行人:超人不会飞\\n本次操作覆盖 63 条 【怪兽线索】，执行成功 63 条， 失败 0 条。 详细执行结果请点击查看详情\",\"receiverChannelType\":1,\"receiverChannelData\":\"{\\\"appId\\\":\\\"crmNotify\\\"}\",\"generateUrlType\":0,\"extraDataMap\":{}}", SendTextMessageArg.class);
        Boolean is = crmMessagePullLimiter.messageIsPull(85903, "CstmCtrl_5FrKx__c", arg);
        System.out.println(is);
    }
}
