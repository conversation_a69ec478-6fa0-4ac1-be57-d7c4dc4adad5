package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 外部OA人员数据表实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName(value = "outer_oa_employee_data", autoResultMap = true)
public class OuterOaEmployeeDataEntity {
    /**
     * 数据库唯一id
     */
    @TableId
    @TableField(fill = FieldFill.INSERT)
    private String id;

    /**
     * 外部企业账号
     */
    private String outEa;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部人员id
     */
    private String outUserId;

    /**
     * 应用id
     */
    private String appId;

    /**
     * 外部人员数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject outUserInfo;

    /**
     * 直属部门id
     */
    private String outDeptId;

    /**
     * 动态查询字段1
     */
    private String text1;

    /**
     * 动态查询字段2
     */
    private String text2;

    /**
     * 动态查询字段3
     */
    private String text3;

    /**
     * 动态查询字段4
     */
    private String text4;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}