package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityField;
import com.facishare.open.outer.oa.connector.common.api.annotation.SecurityObj;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@TableName("outer_oa_enterprise_bind")
@SecurityObj(value = OuterOaEnterpriseBindEntity.class)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OuterOaEnterpriseBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String id; // 数据中心id

    private ChannelEnum channel; // 渠道

    private String fsEa; // 纷享企业 ea

    private String outEa; // 外部企业 ea

    private String appId; // 外部应用 appId

    @SecurityField
    private String connectInfo; // 连接参数

    /**
     * 绑定类型
     */
    private BindTypeEnum bindType;
    /**
     * 绑定状态
     */
    private BindStatusEnum bindStatus;

    private Long createTime; // 创建时间

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime; // 修改时间
}
