package com.facishare.open.oa.base.dbproxy.transfer.service;

import com.facishare.open.oa.base.dbproxy.transfer.handler.OaRateLimitPageTransferHandler;
import com.facishare.transfer.handler.TransferHandler;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 * @IgnoreI18nFile
 */
@Slf4j
public class TransferoService {

    @Autowired
    private ErpDataTransferMgr dataTransferMgr;

    @Autowired
    private MergeJedisCmd publishRedis;

    @Autowired
    private List<OaRateLimitPageTransferHandler<?, ?, ?>> transferHandlerList;

    public Map<String, List<String>> transfer(List<String> eis, List<String> transferHandlerNames,
                                              List<String> blackTransferHandlerNames, Boolean forceTransfer, Boolean allHandler, Boolean allTenant, Long createTime, String endTime) throws ParseException {
        final List<OaRateLimitPageTransferHandler<?, ?, ?>> transferObjectHandlers = getTransferObjectHandlers(
                transferHandlerNames, blackTransferHandlerNames, allHandler);
        if (CollectionUtils.isEmpty(transferObjectHandlers)) {
            throw new RuntimeException("找不到迁移表");
        }

        final Set<String> tenantIds = getTenantIds(eis, createTime, allTenant, transferObjectHandlers);
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new RuntimeException("找不到迁移企业");
        }
        dataTransferMgr.setStopTime(getStopTime(endTime));

        try {
            final Map<String, List<String>> errorEnterpriseId = transferObjectHandlers.stream().parallel()
                    .map(handler -> {
                        List<String> errTenantIds = dataTransferMgr.batchTransfer(tenantIds, forceTransfer, handler,
                                true);

                        if (CollectionUtils.isEmpty(errTenantIds)) {
                            return null;
                        }

                        return Pair.of(handler.getDataType(), errTenantIds);
                    }).filter(Objects::nonNull).collect(Collectors.toMap(Pair::getKey, Pair::getValue));

            log.info("end transferToPaas tenantIds:{}, errorEnterpriseId:{}", tenantIds, errorEnterpriseId);
            return errorEnterpriseId;
        } finally {
            dataTransferMgr.setStopTime(null);
        }
    }

    private Set<String> getAllTenantIds(List<OaRateLimitPageTransferHandler<?, ?, ?>> transferObjectHandlers) {
        return transferObjectHandlers.stream().map(OaRateLimitPageTransferHandler::getAllTenantIds)
                .flatMap(Collection::stream).collect(Collectors.toSet());
    }

    private static Long getStopTime(final String endTime) throws ParseException {
        final Long stopTime;
        if (StringUtils.isEmpty(endTime)) {
            // 没给时间默认10天
            stopTime = null;
        } else {
            if (endTime.contains(" ")) {
                stopTime = DateUtils.parseDate(endTime, "yyyy-MM-dd hh:mm").getTime();
            } else {
                String format = DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd ") + endTime;
                long time = DateUtils.parseDate(format, "yyyy-MM-dd hh:mm").getTime();
                if (System.currentTimeMillis() > time) {
                    time += 1000L * 3600 * 24;
                }
                stopTime = time;
            }
        }
        return stopTime;
    }

    private List<OaRateLimitPageTransferHandler<?, ?, ?>> getTransferObjectHandlers(List<String> transferHandlerNames,
                                                                                    List<String> blackTransferHandlerNames,
                                                                                    Boolean allHandler) {
        List<String> finalTransferHandlerNames = CollectionUtils.isEmpty(transferHandlerNames) ? Lists.newArrayList() : transferHandlerNames;
        List<String> finalBlackTransferHandlerNames = CollectionUtils.isEmpty(blackTransferHandlerNames) ? Lists.newArrayList() : blackTransferHandlerNames;
        return transferHandlerList.stream()
                .filter(t -> allHandler || finalTransferHandlerNames.contains(t.getDataType()))
                .filter(t -> !finalBlackTransferHandlerNames.contains(t.getDataType()))
                .collect(Collectors.toList());
    }

    public void cleanCache() {
        for (OaRateLimitPageTransferHandler<?, ?, ?> oaRateLimitPageTransferHandler : transferHandlerList) {
            oaRateLimitPageTransferHandler.cleanCache();
        }
    }

    public Set<String> resetTransferTask(List<String> eis, List<String> transferHandlerNames, List<String> blackTransferHandlerNames, Boolean allHandler, Boolean allTenant, Long createTime, Boolean addTask) {
        final List<OaRateLimitPageTransferHandler<?, ?, ?>> transferObjectHandlers = getTransferObjectHandlers(
                transferHandlerNames, blackTransferHandlerNames, allHandler);
        if (CollectionUtils.isEmpty(transferObjectHandlers)) {
            throw new RuntimeException("找不到迁移表");
        }

        final Set<String> tenantIds = getTenantIds(eis, createTime, allTenant, transferObjectHandlers);
        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new RuntimeException("找不到迁移企业");
        }

        if (CollectionUtils.isEmpty(tenantIds)) {
            throw new RuntimeException("找不到迁移企业");
        }

        final String[] tenants = tenantIds.toArray(new String[0]);
        final String app = System.getProperty("app.name");
        final String prefix = "transferTask:" + app + ":";
        final List<String> handlers = transferObjectHandlers.stream()
                .map(TransferHandler::getDataType)
                .collect(Collectors.toList());

        handlers.forEach(dataType -> {
            final String key = prefix + dataType;
            final String[] tenants1;

            if (BooleanUtils.isNotTrue(addTask)) {
                tenants1 = tenants;
            } else {
                // 如果addTask为true，则将tenantIds和redis中已有的tenantIds合并，然后重新插入到队列中,需要去重
                final Set<String> tenantSet = new HashSet<>(Arrays.asList(tenants));
                final Set<String> redisTenantSet = new HashSet<>(publishRedis.smembers(key));
                tenantSet.addAll(redisTenantSet);
                tenants1 = tenantSet.toArray(new String[0]);
            }
            
            publishRedis.del(key);
            publishRedis.lpush(key, tenants1);
            publishRedis.expire(key, 60L * 60 * 24 * 7);
        });

        log.warn("resetTransferTask tenantSize:{} tenantIds:{} handlers:{}", tenantIds.size(), tenantIds, handlers);

        return tenantIds;
    }

    private Set<String> getTenantIds(List<String> eis, Long createTime, Boolean allTenant, List<OaRateLimitPageTransferHandler<?, ?, ?>> transferObjectHandlers) {
        if (CollectionUtils.isNotEmpty(eis)) {
            return Sets.newHashSet(eis);
        }
        if (Objects.nonNull(createTime)) {
            return transferObjectHandlers.stream()
                    .map(handler -> handler.getTenantIdsByTime(createTime))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toSet());
        }

        if (BooleanUtils.isTrue(allTenant)) {
            return getAllTenantIds(transferObjectHandlers);
        }

        return new HashSet<>();
    }

    public Map<String, List<String>> processTransferTask(Boolean forceTransfer, List<String> transferHandlerNames, List<String> blackTransferHandlerNames, Boolean allHandler, String endTime) throws ParseException {
        final List<OaRateLimitPageTransferHandler<?, ?, ?>> transferObjectHandlers = getTransferObjectHandlers(
                transferHandlerNames, blackTransferHandlerNames, allHandler);
        if (CollectionUtils.isEmpty(transferObjectHandlers)) {
            throw new RuntimeException("找不到迁移表");
        }

        final String prefix = "transferTask:" + System.getProperty("app.name") + ":";

        dataTransferMgr.setStopTime(getStopTime(endTime));
        Map<String, List<String>> errorTask = new HashMap<>();

        try {
            transferObjectHandlers.stream().parallel()
                    .forEach(handler -> {
                        final String dataType = handler.getDataType();
                        final String key = prefix + dataType;
                        while (true) {
                            final Long llen = publishRedis.llen(key);
                            if (Objects.equals(llen, 0L)) {
                                publishRedis.del(key);
                                log.info("processTransferTask {} end", dataType);
                                return;
                            }

                            final List<String> tenantIds = new ArrayList<>();
                            for (int i = 0; i < 100; i++) {
                                final String ei = publishRedis.lpop(key);
                                if (StringUtils.isEmpty(ei)) {
                                    break;
                                }
                                tenantIds.add(ei);
                            }
                            if (CollectionUtils.isEmpty(tenantIds)) {
                                log.info("processTransferTask {} end", dataType);
                                return;
                            }

                            log.info("processTransferTask {} 剩余任务数量:{} , tenantIds:{}", dataType, llen, tenantIds);

                            final List<String> strings = dataTransferMgr.batchTransfer(tenantIds, forceTransfer, handler, true);
                            if (CollectionUtils.isNotEmpty(strings)) {
                                errorTask.computeIfAbsent(dataType, k -> new ArrayList<>()).addAll(strings);
                            }
                        }
                    });

            log.info("processTransferTask end error:{}", errorTask);
        } finally {
            dataTransferMgr.setStopTime(null);
        }

        return errorTask;
    }
}
