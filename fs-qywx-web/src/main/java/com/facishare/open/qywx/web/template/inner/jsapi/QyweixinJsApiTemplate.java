package com.facishare.open.qywx.web.template.inner.jsapi;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.jsapi.JsApiTemplate;
import com.facishare.open.qywx.web.template.model.JsApiModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinJsapiSignature;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class QyweixinJsApiTemplate extends JsApiTemplate {
    @Resource
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Override
    public void getJsApiSignature(MethodContext context) {
        log.info("QyweixinJsApiTemplate.getJsApiSignature,context={}",context);

        JsApiModel jsApiModel = context.getData();
        Result<QyweixinJsapiSignature> result = qyweixinAccountSyncService.createJsapiSignature2(jsApiModel.getUrl(), jsApiModel.getFsEa(), jsApiModel.getAppId(), jsApiModel.getOutEa());

        log.info("QyweixinJsApiTemplate.getJsApiSignature,result={}",result);
        context.setResult(TemplateResult.newSuccess(result));
    }
}
