package com.facishare.open.qywx.accountsync.excel.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(40)
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentMappingVo {
    /**
     * 纷享EA
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s106",index = 0)
    private String fsEa;

    /**
     * 企微企业ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s107",index = 1)
    private String outEa;

    /**
     * 纷享部门ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s108",index = 2)
    private Integer fsDepId;

    /**
     * 纷享部门名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s109",index = 3)
    private String fsDepName;

    /**
     * 企微部门ID
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s110",index = 4)
    private String outDepId;
    /**
     * 企微部门名称
     */
    @ExcelProperty(value = "erpdss_outer_oa_connector.qywx.global.s111",index = 5)
    private String outDepName;

    public static DepartmentMappingVo getTempData(I18NStringManager i18NStringManager, String lang, String tenantId) {
        DepartmentMappingVo vo = new DepartmentMappingVo();
        vo.setFsEa(i18NStringManager.get(I18NStringEnum.s112.getI18nKey(),lang,tenantId,""));
        vo.setOutEa("");
        vo.setFsDepId(1000);
        vo.setFsDepName("");
        vo.setOutDepId("1");
        vo.setOutDepName("");
        return vo;
    }
}
