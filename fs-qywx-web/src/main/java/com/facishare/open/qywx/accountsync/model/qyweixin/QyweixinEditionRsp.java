package com.facishare.open.qywx.accountsync.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * Created on 2019/3/11
 */
@Data
public class QyweixinEditionRsp implements Serializable {

    private static final long serialVersionUID = -3582259980244563822L;
    private Integer agentid;
    /**
     付费状态。
     0-没有付费;
     1-限时试用;
     2-试用过期;
     3-购买期内;
     4-购买过期;
     5-不限时试用;
     6-购买期内，但是人数超标, 注意，超标后还可以用7天;
     7-购买期内，但是人数超标, 且已经超标试用7天
     **/
    private Integer app_status;

    /**
     * 购买版本ID
     */
    private String edition_id;

    /**
     * 购买版本名字
     */
    private String edition_name;

    /**
     * 用户上限。特别注意，如果是固定总价的购买，该参数固定为 **********， 含义为不限用户数
     */
    private Long user_limit;

    /**
     * 版本到期时间（根据购买版本，可能是试用到期时间或付费使用到期时间）。特别注意，**********代表不限时间
     */
    private Long expired_time;
}
