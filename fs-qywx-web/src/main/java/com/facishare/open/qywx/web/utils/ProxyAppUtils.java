package com.facishare.open.qywx.web.utils;

import com.facishare.open.qywx.accountsync.model.EnterpriseWechatUserModel;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 纷享代理通工具类
 * <AUTHOR>
 * @date 2024-01-16
 */
@Component
@Slf4j
public class ProxyAppUtils {
    @Resource(name = "outEventDataChangeMQSender")
    private AutoConfRocketMQProducer outEventDataChangeMQSender;

    public void sendEnterpriseWechatUserInfo(EnterpriseWechatUserModel model) {
        log.info("ProxyAppUtils.sendEnterpriseWechatUserInfo,model={}",model);
        Message msg = new Message();
        msg.setTags("enter_agent");
        msg.setBody(model.toProto());

        //把纷享云的MQ投递到所有的专属云
        TraceContext context = TraceContext.get();
        context.setEi("0");

        SendResult sendResult = outEventDataChangeMQSender.send(msg);

        //移除上下文，避免跨云调用混乱
        TraceContext.remove();

        log.info("ProxyAppUtils.sendEnterpriseWechatUserInfo,sendResult={}",sendResult);
    }
}
