package com.facishare.open.qywx.web.service;

import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.arg.SendTextCardMessagePushArg;
import com.facishare.open.qywx.web.arg.SendTextMessagePushArg;

public interface ExternalMsgService {

    /**
     * 处理文本通知消息
     * @param sendTextMessagePushArg
     * @return
     */
    Result<Void> sendTextMessage(SendTextMessagePushArg sendTextMessagePushArg);

    /**
     * 处理卡片通知消息
     * @param sendTextCardMessagePushArg
     * @return
     */
    Result<Void> sendTextCardMessage(SendTextCardMessagePushArg sendTextCardMessagePushArg);
}
