package com.facishare.open.qywx.web.service;

import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.arg.CreateTodoPushArg;
import com.facishare.open.qywx.web.arg.DealTodoPushArg;
import com.facishare.open.qywx.web.arg.DeleteTodoPushArg;

public interface ExternalTodoMsgService {

    /**
     * 新增待办消息
     * @param createTodoPushArg
     * @return
     */
    Result<Void> createTodo(CreateTodoPushArg createTodoPushArg);

    /**
     * 处理待办消息
     * @param dealTodoPushArg
     * @return
     */
    Result<Void> dealTodo(DealTodoPushArg dealTodoPushArg);

    /**
     * 删除待办消息
     * @param deleteTodoPushArg
     * @return
     */
    Result<Void> deleteTodo(DeleteTodoPushArg deleteTodoPushArg);
}
