package com.facishare.open.qywx.web.manager;//package com.facishare.open.qywx.accountsync.manager;
//
//import com.alibaba.fastjson.JSON;
//import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
//import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
//import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
//import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
//import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinCorpInfo;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDepartmentInfo;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinEmployeeInfo;
//import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinTagEmployeeList;
//import com.facishare.open.qywx.accountsync.result.Result;
//import com.facishare.open.qywx.accountsync.service.impl.QyweixinAccountSyncServiceImpl;
//import com.facishare.open.qywx.accountsync.utils.UserRoleUtils;
//import com.facishare.open.qywx.accountsync.utils.WechatRegisterConfigHelper;
//import com.facishare.organization.adapter.api.config.model.SetConfigDto;
//import com.facishare.organization.adapter.api.config.service.EnterpriseConfigService;
//import com.facishare.organization.adapter.api.model.EnterpriseConfigKey;
//import com.facishare.organization.adapter.api.model.PermissionSpecialOperator;
//import com.facishare.organization.adapter.api.model.biz.RunStatus;
//import com.facishare.organization.adapter.api.model.biz.employee.arg.GetAllEmployeeIdsArg;
//import com.facishare.organization.adapter.api.model.biz.employee.result.GetAllEmployeeIdsResult;
//import com.facishare.organization.adapter.api.model.biz.enterprise.InitRegisterEnterprise;
//import com.facishare.organization.adapter.api.model.biz.enterprise.InitRegisterEnterpriseWithOutSqlserver;
//import com.facishare.organization.adapter.api.service.EmployeeService;
//import com.facishare.organization.adapter.api.service.EnterpriseService;
//import com.facishare.register.api.dto.*;
//import com.facishare.register.api.service.CoreRegisterService;
//import com.facishare.uc.api.model.employee.arg.CreateEmployeeWithPasswordArg;
//import com.facishare.uc.api.model.employee.result.CreateEmployeeWithPasswordResult;
//import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
//import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
//import com.facishare.uc.api.service.EmployeeEditionService;
//import com.facishare.uc.api.service.EnterpriseEditionService;
//import com.facishare.webhook.api.ProductDefine;
//import com.facishare.webhook.api.dto.AddCrmOrderDto;
//import com.facishare.webhook.api.dto.CrmOrderToDetailDto;
//import com.facishare.webhook.api.model.CrmOrderDetailInfo;
//import com.facishare.webhook.api.model.CrmOrderProductInfo;
//import com.facishare.webhook.api.service.VersionRegisterService;
//import com.facishare.webhook.common.dao.DepartmentDao;
//import com.facishare.webhook.common.dao.EmployeeDao;
//import com.facishare.webhook.common.dao.EnterpriseDao;
///*import com.facishare.webhook.common.dao.paas.dao.CrmCustomer;
//import com.facishare.webhook.common.dao.paas.dao.CrmRegisterThread;
//import com.facishare.webhook.common.dao.paas.enums.CrmCustomerSource;*/
//import com.facishare.webhook.common.model.CRMUserRoleResult;
//import com.facishare.webhook.common.model.OrderType;
//import com.facishare.webhook.common.model.RegisterObject;
//import com.facishare.webhook.common.model.ResourceSource;
//import com.facishare.webhook.common.service.*;
//import com.facishare.webhook.common.util.DateHelper;
//import com.facishare.webhook.common.util.DateUtil;
//import com.facishare.webhook.common.util.MD5Util;
//import com.facishare.webhook.common.util.StringHelper;
//import com.github.autoconf.spring.reloadable.ReloadableProperty;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import com.google.gson.Gson;
//import com.google.gson.reflect.TypeToken;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.collections4.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static com.facishare.webhook.common.util.Constant.FIRST_EMPLOYEE_ID;
//import static com.facishare.webhook.common.util.Constant.OPERATOR;
//
///**
// * Created by mawenrui on 2018/7/19.
// */
//@Service
//@Slf4j
//public class QiyeweixinRegisterService implements RegisterService, InitializingBean {
//
//    @Autowired
//    private EnterpriseDao enterpriseDao;
//    @Autowired
//    private DepartmentDao departmentDao;
//    @Autowired
//    private EmployeeDao employeeDao;
//    @Autowired
//    private CoreRegisterService coreRegisterService;
//    @Autowired
//    private EnterpriseConfigService enterpriseConfigService;
//    @Autowired
//    private QyweixinAccountBindService qyweixinAccountBindService;
//    @Resource
//    private QyweixinAccountSyncServiceImpl qyweixinAccountSyncServiceImpl;
//    @Autowired
//    private EnterpriseEditionService enterpriseEditionService;
//    @Autowired
//    private QiyeweixinOrganizationService qiyeweixinOrganizationService;
//    @Autowired
//    private EmployeeService employeeService;
//    @Autowired
//    private SMSService smsService;
//    @Autowired
//    private WechatMessageService wechatMessageService;
//    @Autowired
//    private EnterpriseService enterpriseService;
//    @Autowired
//    private EmployeeEditionService employeeEditionService;
//    @Resource(name = "commonCrmObjectService")
//    private CrmObjectService crmObjectService;
//    @Autowired
//    private VersionRegisterService versionRegisterService;
//    @Autowired
//    private OrgNameService orgNameService;
//    @Autowired
//    private UserRoleUtils userRoleUtils;
//
//    @ReloadableProperty("contactAppId")
//    private String contactAppId;
//    @ReloadableProperty("crmAppId")
//    private String crmAppId;
//    @ReloadableProperty("marketingAppId")
//    private String marketingAppId;
//    @ReloadableProperty("eserviceAppId")
//    private String eserviceAppId;
//    @ReloadableProperty("editionMapping")
//    private String editionMapping;
//    @ReloadableProperty("createOrderMapping")
//    private String createOrderMapping;
//
//    private List<TaskFunction<String, RegisterObject>> processorTasks;
//
//    public static Map<String, ProductDefine> productDefineMapping = Maps.newConcurrentMap();
//
//    static {
//        productDefineMapping.put("QYWX_TRY", ProductDefine.QYWX_TRY);
//        productDefineMapping.put("QYWX_BASE", ProductDefine.QYWX_BASE);
//        productDefineMapping.put("QYWX_PRO", ProductDefine.QYWX_PRO);
//        productDefineMapping.put("QYWX_ESERVICE_TRY", ProductDefine.QYWX_ESERVICE_TRY);
//        productDefineMapping.put("QYWX_ESERVICE_BASE", ProductDefine.QYWX_ESERVICE_BASE);
//        productDefineMapping.put("STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION", ProductDefine.STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION);
//        productDefineMapping.put("INTERCONNECT_APP_BASIC", ProductDefine.INTERCONNECT_APP_BASIC);
//        productDefineMapping.put("ADVANCED_OUTWORK_APP", ProductDefine.ADVANCED_OUTWORK_APP);
//        log.info("init productDefineMapping success. productDefineMapping:{}", productDefineMapping);
//    }
//
//    private static final ResourceSource resourceSource = ResourceSource.SELF_WEIXIN;
//    /**
//     * 标识试用版本枚举的key
//     */
//    private static final String TRY_EDITION_ENUM_NAME = "tryEditionEnumName";
//
//    private static Random random = new Random();
//
//    @Override
//    public void saveOutData(String openInfo, RegisterObject registerObject) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//
//        String eid = qyweixinAddEnterprise.getCorpId();
//
//        // 企业信息
//        enterpriseDao.findAndModifyEnterprise(resourceSource.getWebhookSource(), eid, openInfo);
//    }
//
//    @Override
//    public void initRegisterObject(String openInfo, RegisterObject registerObject) {
//
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        String eid = qyweixinAddEnterprise.getCorpId();
//        String appId = qyweixinAddEnterprise.getAppId();
//
//        // 只是更新可见范围
//        if (Objects.isNull(qyweixinAddEnterprise.getOrderType())) {
//            return;
//        }
//
//        // 更新customer
//        registerObject.getCustomer().setName(qyweixinAddEnterprise.getCorpName().replaceAll(StringUtils.SPACE, StringUtils.EMPTY));
//        registerObject.getCustomer().setEnterpriseOpenId(eid);
//        registerObject.getCustomer().setAppId(qyweixinAddEnterprise.getAppId());
//
//        // 更新order
//        // 0-普通订单，1-扩容订单，2-续期，3-版本变更，4-试用（若是试用则下面字段都为空）
//        switch (qyweixinAddEnterprise.getOrderType()) {
//            case 0:
//            case 1:
//            case 2:
//            case 3:
//                // CrmCount为购买应用的人数
//                registerObject.getOrder().setCrmCount(qyweixinAddEnterprise.getUserCount());
//                registerObject.getOrder().setCrmAmount(String.valueOf(qyweixinAddEnterprise.getPrice() / 100));
//                registerObject.getOrder().setOrderAmount(String.valueOf(qyweixinAddEnterprise.getPrice() / 100));
//                registerObject.getOrder().setOperatorId(qyweixinAddEnterprise.getOperatorId());
//                registerObject.getOrder().setOrderId("qywx" + qyweixinAddEnterprise.getOrderId());
//                registerObject.getOrder().setOrderType(OrderType.BUY.getValue());
//                registerObject.getOrder().setCrmBeginDate(qyweixinAddEnterprise.getBeginTime());
//                registerObject.getOrder().setCrmEndDate(qyweixinAddEnterprise.getEndTime());
//
//                String editionId = qyweixinAddEnterprise.getEditionId();
//                setOrderProductDefine(appId, editionId, registerObject);
//                break;
//            case 4:
//                registerObject.getOrder().setCrmCount(500);
//                registerObject.getOrder().setCrmAmount("0");
//                registerObject.getOrder().setOrderAmount("0");
//                registerObject.getOrder().setOperatorId(qyweixinAddEnterprise.getUserId());
//                registerObject.getOrder().setOrderId("qywx" + qyweixinAddEnterprise.getCorpId() + qyweixinAddEnterprise.getAppId());
//                registerObject.getOrder().setOrderType(OrderType.TRY.getValue());
//
//                SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
//                registerObject.getOrder().setCrmBeginDate(formatter.format(new Date()));
//                registerObject.getOrder().setCrmEndDate(qyweixinAddEnterprise.getEndTime());
//                ProductDefine productDefine = getProductDefine(appId, TRY_EDITION_ENUM_NAME);
//                if (productDefine != null) {
//                    registerObject.getOrder().setProductDefine(productDefine);
//                }
//                break;
//            default:
//                throw new RuntimeException("开通信息里没有订单类型(企业微信版本太低)或者不支持此订单类型");
//        }
//
//        registerObject.getOrder().setOperatorPhone(StringUtils.EMPTY);
//        registerObject.getOrder().setOperatorName(orgNameService.formatEmployeeName(qyweixinAddEnterprise.getUserName()));
//
//        enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), eid, JSON.toJSONString(registerObject));
//    }
//
//    /**
//     * 设置订单对应的产品信息
//     */
//    public void setOrderProductDefine(String appId, String editionId, RegisterObject registerObject) {
//        ProductDefine productDefine = getProductDefine(appId, editionId);
//        if (productDefine != null) {
//            registerObject.getOrder().setProductDefine(productDefine);
//        }
//    }
//
//    private ProductDefine getProductDefine(String appId, String editionId) {
//        Map<String, Map<String, String>> editionMappingMap = new Gson().fromJson(editionMapping, new TypeToken<Map<String, Map<String, String>>>() {
//        }.getType());
//        Map<String, String> versionMapping = editionMappingMap.get(appId);
//        if (MapUtils.isEmpty(versionMapping)) {
//            log.error("get versionMapping failed. appId:{}, editionId:{}, editionMappingMap:{}", appId, editionId, editionMappingMap);
//            return null;
//        }
//        String productDefineName = versionMapping.get(editionId);
//        if (StringUtils.isBlank(productDefineName)) {
//            log.error("get productDefineName failed. appId:{}, editionId:{}, editionMappingMap:{}", appId, editionId, editionMappingMap);
//            return null;
//        }
//        log.info("getProductDefine success. appId:{}, editionId:{}", appId, editionId);
//        return productDefineMapping.get(productDefineName);
//    }
//
//    @Override
//    public Integer createEnterprise(String openInfo, RegisterObject registerObject) {
//        com.facishare.open.qywx.accountbind.result.Result<String> accountBindResult =
//                qyweixinAccountBindService.outEaToFsEa(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId());
//
//        if (!accountBindResult.isSuccess()) {
//            throw new RuntimeException("qyweixinAccountBindService.outEaToFsEa ex:" + accountBindResult.getErrorCode());
//        }
//
//        String fsEA = accountBindResult.getData();
//
//        if (StringUtils.isNotBlank(fsEA)) {
//            GetEnterpriseDataArg getEnterpriseDataArg = new GetEnterpriseDataArg();
//            getEnterpriseDataArg.setEnterpriseAccount(fsEA);
//            GetEnterpriseDataResult enterpriseData = enterpriseEditionService.getEnterpriseData(getEnterpriseDataArg);
//
//            if (enterpriseData.getEnterpriseData().getRunStatus() == 2) {
//                throw new RuntimeException("企业已正常开通");
//            }
//
//            registerObject.getCustomer().setEnterpriseId(enterpriseData.getEnterpriseData().getEnterpriseId());
//            registerObject.getCustomer().setEnterpriseAccount(enterpriseData.getEnterpriseData().getEnterpriseAccount());
//
//            log.debug("initRegisterObject updateRegisterObject");
//            enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId(), JSON.toJSONString(registerObject));
//
//        } else {
//            // 创建企业
//            CreateEnterpriseDto.Argument createEnterpriseArg = new CreateEnterpriseDto.Argument();
//            createEnterpriseArg.setCreatorName(registerObject.getOrder().getOperatorName());
//            createEnterpriseArg.setSource(resourceSource.getFsCoreDefine());
//            createEnterpriseArg.setCreatorEmail(StringUtils.EMPTY);
//
//            createEnterpriseArg.setEnterpriseName(registerObject.getCustomer().getName());
//
//            String userPhone = StringUtils.EMPTY;
//            if (StringUtils.isNotBlank(registerObject.getOrder().getOperatorPhone())) {
//                userPhone = registerObject.getOrder().getOperatorPhone();
//            }
//            createEnterpriseArg.setCreatorMobile(userPhone);
//
//            CreateEnterpriseDto.Result createEnterpriseResult = coreRegisterService.createEnterprise(createEnterpriseArg);
//            log.info("createEnterprise end. createEnterpriseArg:{}, createEnterpriseResult:{}", createEnterpriseArg, createEnterpriseResult);
//            if (createEnterpriseResult.getCreateSuccess()) {
//                registerObject.getCustomer().setEnterpriseId(createEnterpriseResult.getEnterpriseId());
//                registerObject.getCustomer().setEnterpriseAccount(createEnterpriseResult.getEnterpriseAccount());
//
//                log.info("initRegisterObject updateRegisterObject");
//                enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId(), JSON.toJSONString(registerObject));
//
//                // 绑定新建企业
//                QyweixinAccountEnterpriseMapping arg = new QyweixinAccountEnterpriseMapping();
//                arg.setSource(resourceSource.getWebhookSource());
//                arg.setOutEa(registerObject.getCustomer().getEnterpriseOpenId());
//                arg.setFsEa(createEnterpriseResult.getEnterpriseAccount());
//                qyweixinAccountBindService.bindAccountEnterpriseMapping(arg);
//            } else {
//                throw new RuntimeException("create enterprise fail");
//            }
//
//            // 更新DBServerIP
//            SetDBServerIPDto.Argument argument = new SetDBServerIPDto.Argument();
//            argument.setEnterpriseAccount(registerObject.getCustomer().getEnterpriseAccount());
//            argument.setSource(resourceSource.getFsCoreDefine());
//            coreRegisterService.setDBServerIP(argument);
//
//            // 行业和规模
//            Result<QyweixinCorpInfo> qyweixinCorpInfoResult =
//                    qyweixinAccountSyncServiceImpl.getCorpInfo(registerObject.getCustomer().getEnterpriseAccount(), registerObject.getCustomer().getAppId());
//            if (!qyweixinCorpInfoResult.isSuccess()) {
//                throw new RuntimeException("getCorpInfo 获取不到企业信息");
//            }
//
//            registerObject.getCustomer().setIndustry(qyweixinCorpInfoResult.getData().getCorpIndustry());
//            registerObject.getCustomer().setScale(qyweixinCorpInfoResult.getData().getCorpScale());
//            registerObject.getCustomer().setSubIndustry(qyweixinCorpInfoResult.getData().getCorpSubIndustry());
//            registerObject.getCustomer().setCorpType(qyweixinCorpInfoResult.getData().getCorpType());
//        }
//
//        return registerObject.getCustomer().getEnterpriseId();
//    }
//
//    @Override
//    public void sendOpenMessage(String openInfo, RegisterObject registerObject) {
//        SendEnterpriseOpenMessageDto.Argument argument = new SendEnterpriseOpenMessageDto.Argument();
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        coreRegisterService.sendEnterpriseOpenMessage(argument);
//    }
//
//    @Override
//    public void updateEnterpriseRunstatusToNormal(String openInfo, RegisterObject registerObject) {
//        UpdateEnterpriseRunstatusDto.Argument argument = new UpdateEnterpriseRunstatusDto.Argument();
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        argument.setRunstatus(2);
//        coreRegisterService.updateEnterpriseRunstatus(argument);
//    }
//
//    @Override
//    public void syncToHJOrPaas(String openInfo, RegisterObject registerObject) {
//        // 老企业增购续费时，还没有客户，新建一个，否则不能下单
//        /*List<CrmCustomer> crmCustomers = crmObjectService.getCustomerByKey(CRM_CUSTOMER_OBJECT_API_ENTERPRISE_ACCOUNT, registerObject.getCustomer().getEnterpriseAccount());
//        if (CollectionUtils.isEmpty(crmCustomers)) {
//            createCustomer(openInfo, registerObject);
//        } else {
//            registerObject.getCustomer().set_id(crmCustomers.get(0).getId());
//            enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId(), JSON.toJSONString(registerObject));
//        }*/
//
//        // 非首次下单，不需要填写管理员信息, 也不需要填写企业账号
//        /*createOrder(openInfo, registerObject);
//        createThread(openInfo, registerObject);*/
//    }
//
//    @Override
//    public void createDB(String openInfo, RegisterObject registerObject) {
//        CreateDBDto.Argument argument = new CreateDBDto.Argument();
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        CreateDBDto.Result result = coreRegisterService.createDB(argument);
//        if (!result.getIsSuccess()) {
//            throw new RuntimeException("create database fail");
//        }
//    }
//
//    @Override
//    public void initDBData(String openInfo, RegisterObject registerObject) {
//        InitDBDataDto.Argument argument = new InitDBDataDto.Argument();
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        argument.setManagerPassword(registerObject.getCustomer().getPassword());
//        InitDBDataDto.Result result = coreRegisterService.initDBData(argument);
//
//        if (!result.getIsSuccess()) {
//            log.error("initDBData failed. argument:{}, result:{}", argument, result);
//            throw new RuntimeException("init database data fail");
//        }
//    }
//
//    @Override
//    public void createAdmin(String openInfo, RegisterObject registerObject) {
//
//        // 员工账号
//        String employeeAccount = registerObject.getOrder().getOperatorId().replaceAll("-", StringUtils.EMPTY);
//        if (employeeAccount.length() < 2) {
//            employeeAccount += "a";
//        }
//        if (employeeAccount.length() > 50) {
//            employeeAccount = employeeAccount.substring(0, 49);
//        }
//        String initPassword = "a1" + StringHelper.getRandomCode(6);
//        // mongo通讯录员工
//        InitRegisterEnterprise.Argument argument = new InitRegisterEnterprise.Argument();
//
//        argument.setEmployeeId(FIRST_EMPLOYEE_ID);
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        argument.setCurrentEmployeeId(PermissionSpecialOperator.MQ_BY_REGISTER.getOperatorId());
//
//        argument.setAccount(employeeAccount);
//        argument.setFullName(registerObject.getOrder().getOperatorName());
//        argument.setName(registerObject.getOrder().getOperatorName());
//        argument.setPost(StringUtils.EMPTY);
//        argument.setGender("F");
//        argument.setMobile(registerObject.getOrder().getOperatorPhone());
//        argument.setEmail(StringUtils.EMPTY);
//        argument.setMobileStatus(1);
//        argument.setPassword(MD5Util.getMD5(initPassword));
//        argument.setPasswordEncodeType(1);
//
//        InitRegisterEnterpriseWithOutSqlserver.Result InitRegisterEnterpriseResult = enterpriseService.initRegisterEnterpriseWithOutSqlserver(argument);
//
//        // UC员工
//        CreateEmployeeWithPasswordArg createEmployeeWithPasswordArg = new CreateEmployeeWithPasswordArg();
//        createEmployeeWithPasswordArg.setEmployeeId(FIRST_EMPLOYEE_ID);
//        createEmployeeWithPasswordArg.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        createEmployeeWithPasswordArg.setTrialVersionExpireTime(DateHelper.getFSMinTime().getTime());
//        createEmployeeWithPasswordArg.setVersionType(0);
//        createEmployeeWithPasswordArg.setIgnoreMobileLimit(false);
//        createEmployeeWithPasswordArg.setActivated(true);
//        createEmployeeWithPasswordArg.setInitialPassword(false);
//        createEmployeeWithPasswordArg.setPauseLogin(false);
//
//        createEmployeeWithPasswordArg.setEmployeeAccount(employeeAccount);
//
//        createEmployeeWithPasswordArg.setPassword(MD5Util.getMD5(initPassword));
//
//        registerObject.getCustomer().setPassword(initPassword);
//
//        log.debug("initRegisterObject updateRegisterObject. argument:{}, InitRegisterEnterpriseResult:{}", argument, InitRegisterEnterpriseResult);
//        enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId(), JSON.toJSONString(registerObject));
//
//        createEmployeeWithPasswordArg.setPasswordEncodeType(1);
//        createEmployeeWithPasswordArg.setMobile(registerObject.getOrder().getOperatorPhone());
//
//        List<Integer> freePackage = Lists.newArrayList(30, 15, 10, 5);
//        createEmployeeWithPasswordArg.setFreePackage(freePackage);
//
//        CreateEmployeeWithPasswordResult passwordResult = employeeEditionService.createEmployeeWithPassword(createEmployeeWithPasswordArg);
//        log.info("createAdmin success. createEmployeeWithPasswordArg:{}, InitRegisterEnterpriseResult:{}, passwordResult:{}",
//                createEmployeeWithPasswordArg, InitRegisterEnterpriseResult, passwordResult);
//    }
//
//    @Override
//    public void adminBinder(String openInfo, RegisterObject registerObject) {
//        List<QyweixinAccountEmployeeMapping> accountEmployeeMappings = Lists.newArrayList();
//
//        QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
//        accountEmployeeMapping.setSource(resourceSource.getWebhookSource());
//        accountEmployeeMapping.setFsAccount("E." + registerObject.getCustomer().getEnterpriseAccount() + "." + FIRST_EMPLOYEE_ID);
//        accountEmployeeMapping.setOutAccount(registerObject.getOrder().getOperatorId());
//        accountEmployeeMapping.setOutEa(registerObject.getCustomer().getEnterpriseOpenId());
//        accountEmployeeMapping.setAppId(registerObject.getCustomer().getAppId());
//
//        accountEmployeeMappings.add(accountEmployeeMapping);
//
//        qyweixinAccountBindService.bindAccountEmployeeMapping(accountEmployeeMappings);
//    }
//
//    @Override
//    public void lanuchEnterprise(String openInfo, RegisterObject registerObject) {
//        LaunchEnterpriseDto.Argument argument = new LaunchEnterpriseDto.Argument();
//        argument.setEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//        LaunchEnterpriseDto.Result result = coreRegisterService.launchEnterprise(argument);
//
//        if (!result.getIsSuccess()) {
//            throw new RuntimeException("lanuch enterprise fail");
//        }
//    }
//
//    @Override
//    public void setEnterpriseConfig(String openInfo, RegisterObject registerObject) {
//        setConfig(registerObject.getCustomer().getEnterpriseId(), EnterpriseConfigKey.ENTERPRISE_NO_PASSWORD_LOGIN.getKey(), "1");
//    }
//
//    private void setConfig(Integer enterpriseId, String key, String value) {
//        SetConfigDto.Argument argument = new SetConfigDto.Argument();
//        argument.setEnterpriseId(enterpriseId);
//        argument.setCurrentEmployeeId(-8);
//        argument.setKey(key);
//        argument.setValue(value);
//        enterpriseConfigService.setConfig(argument);
//    }
//
//    /**
//     * 只有首次开通企业时调用
//     */
//    @Override
//    public void syncDepartmentTask(String openInfo, RegisterObject registerObject) {
//
//        List<QyweixinDepartmentInfo> departmentInfoList = new ArrayList<>();
//        String appId = registerObject.getCustomer().getAppId();
//        String ea = registerObject.getCustomer().getEnterpriseAccount();
//
//        // 1. 获取全公司的部门，包含tag下的部门
//        Result<List<QyweixinDepartmentInfo>> departmentResult = qyweixinAccountSyncServiceImpl.getDepartmentInfoList(ea, appId, StringUtils.EMPTY);
//
//        if (!departmentResult.isSuccess()) {
//            throw new RuntimeException("qyweixinAccountSyncService.getDepartmentInfoList error: " + departmentResult.getErrorCode());
//        }
//
//        if (CollectionUtils.isNotEmpty(departmentResult.getData())) {
//            departmentInfoList.addAll(departmentResult.getData());
//        }
//
//        // 2. 创建部门
//        departmentInfoList.stream().distinct().forEach(qyweixinDepartmentInfo -> {
//            // 创建纷享部门
//            qiyeweixinOrganizationService.createDepartment(
//                    registerObject.getCustomer().getEnterpriseOpenId(),
//                    registerObject.getCustomer().getEnterpriseId(),
//                    registerObject.getCustomer().getEnterpriseAccount(),
//                    JSON.toJSONString(qyweixinDepartmentInfo)
//            );
//        });
//
//        // 创建部门时，先统一设置了父部门为根部门，在这里同步到实际父部门下
//        departmentInfoList.forEach(qyweixinDepartmentInfo -> qiyeweixinOrganizationService.modifyDepartment(
//                registerObject.getCustomer().getEnterpriseOpenId(),
//                registerObject.getCustomer().getEnterpriseId(),
//                registerObject.getCustomer().getEnterpriseAccount(),
//                JSON.toJSONString(qyweixinDepartmentInfo)));
//    }
//
//    /**
//     * 只有企业开通时调用
//     */
//    @Override
//    public void syncEmployeeTask(String openInfo, RegisterObject registerObject) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        List<QyweixinEmployeeInfo> employeeInfoList = Lists.newArrayList();
//        String ea = registerObject.getCustomer().getEnterpriseAccount();
//        String appId = registerObject.getCustomer().getAppId();
//        String outAccount = registerObject.getCustomer().getEnterpriseOpenId();
//
//        // 1. 同步部门下的员工
//        //List<DepartmentPo> departmentPoList = departmentDao.getDepartmentListByEid(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId());
//        qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(), outAccount
//                , appId, Lists.newArrayList()).getData().forEach(mapping -> {
//            Result<List<QyweixinEmployeeInfo>> departmentEmployeeInfoResult = qyweixinAccountSyncServiceImpl.getDepartmentEmployeeInfoList(ea, mapping.getOutDepartmentId());
//
//            if (!departmentEmployeeInfoResult.isSuccess()) {
//                throw new RuntimeException("qyweixinAccountSyncService.getDepartmentEmployeeInfoList error: " + departmentEmployeeInfoResult.getErrorCode());
//            }
//
//            if (CollectionUtils.isNotEmpty(departmentEmployeeInfoResult.getData())) {
//                employeeInfoList.addAll(departmentEmployeeInfoResult.getData());
//            }
//        });
//
//       /* departmentPoList.forEach(departmentPo -> {
//            Result<List<QyweixinEmployeeInfo>> departmentEmployeeInfoResult =
//                    qyweixinAccountSyncServiceImpl.getDepartmentEmployeeInfoList(
//                            registerObject.getCustomer().getEnterpriseAccount(), departmentPo.getDepartmentId());
//
//            if (!departmentEmployeeInfoResult.isSuccess()) {
//                throw new RuntimeException("qyweixinAccountSyncService.getDepartmentEmployeeInfoList error: " + departmentEmployeeInfoResult.getErrorCode());
//            }
//
//            if (Objects.nonNull(departmentEmployeeInfoResult.getData())) {
//                employeeInfoList.addAll(departmentEmployeeInfoResult.getData());
//            }
//        });*/
//
//        // 2. 同步标签下的部门和员工
//        if (Objects.nonNull(qyweixinAddEnterprise.getPrivilege().getAllowTag())) {
//            qyweixinAddEnterprise.getPrivilege().getAllowTag().forEach(qyweixinTag -> {
//                // 处理tag下的员工
//                Result<QyweixinTagEmployeeList> employeeListResult = qyweixinAccountSyncServiceImpl.getTagEmployeeInfoList(
//                        ea, appId, qyweixinTag);
//
//                if (!employeeListResult.isSuccess()) {
//                    throw new RuntimeException("qyweixinAccountSyncService.getTagEmployeeInfoList error: " + employeeListResult.getErrorCode());
//                }
//
//                if (Objects.nonNull(employeeListResult.getData()) && CollectionUtils.isNotEmpty(employeeListResult.getData().getUserList())) {
//                    // 标签下的员工
//                    employeeInfoList.addAll(employeeListResult.getData().getUserList());
//                }
//            });
//        }
//
//        // 3. 同步可见范围内的员工；对于可见范围内的部门和tag，可以用上面的接口获取，所以不再做处理
//        if (Objects.nonNull(qyweixinAddEnterprise.getPrivilege().getAllowUser())) {
//            List<String> outUserIds = Lists.newArrayList(qyweixinAddEnterprise.getPrivilege().getAllowUser());
//
//            Result<List<QyweixinEmployeeInfo>> adminListResult = qyweixinAccountSyncServiceImpl.getEmployeeInfoBatch(
//                    ea, appId, outUserIds);
//            if (!adminListResult.isSuccess()) {
//                throw new RuntimeException("qyweixinAccountSyncService.getEmployeeInfoBatch error: " + adminListResult.getErrorCode());
//            }
//            employeeInfoList.addAll(adminListResult.getData());
//        }
//
//        employeeInfoList.stream().distinct().forEach(qyweixinEmployeeInfo -> {
//            // 企业创建者不需要再次创建
//            if (!qyweixinEmployeeInfo.getUserId().equalsIgnoreCase(registerObject.getOrder().getOperatorId())) {
//                qiyeweixinOrganizationService.createEmployee(
//                        registerObject.getCustomer().getEnterpriseOpenId(),
//                        registerObject.getCustomer().getEnterpriseId(),
//                        registerObject.getCustomer().getEnterpriseAccount(),
//                        JSON.toJSONString(qyweixinEmployeeInfo)
//                );
//            } else {
//                // 企业创建者只需要写入纷享mongo
//                employeeDao.findAndModifyEmployee(
//                        resourceSource.getWebhookSource(),
//                        registerObject.getCustomer().getEnterpriseOpenId(),
//                        qyweixinEmployeeInfo.getUserId(),
//                        JSON.toJSONString(qyweixinEmployeeInfo)
//                );
//            }
//        });
//    }
//
//    @Override
//    public void addPackageConfig(String openInfo, RegisterObject registerObject) {
//
//    }
//
//    public void createOrder(String openInfo, RegisterObject registerObject) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        // 非首次下单，不需要填写管理员信息, 也不需要填写企业账号
//        String enterpriseAccount = StringUtils.EMPTY;
//        if (StringUtils.isNotBlank(registerObject.getCustomer().getEnterpriseAccount())) {
//            enterpriseAccount = registerObject.getCustomer().getEnterpriseAccount();
//        }
//
//        Integer allResourceCount = registerObject.getOrder().getCrmCount();
//        Integer quality = registerObject.getOrder().getCrmCount();
//
//        // 企业微信标准版，单套资源数是5个配额，个数需要除以5
//        if (Objects.equals(ProductDefine.QYWX_TRY, registerObject.getOrder().getProductDefine()) ||
//                Objects.equals(ProductDefine.QYWX_BASE, registerObject.getOrder().getProductDefine())) {
//            quality = allResourceCount / 5;
//        }
//
//        String appId = qyweixinAddEnterprise.getAppId();
//        String editionId = qyweixinAddEnterprise.getEditionId();
//        String finalEnterpriseAccount = enterpriseAccount;
//        Integer finalQuality = quality;
//
//        getProductDefineList(appId, editionId).forEach(productDefine -> {
//            AddCrmOrderDto.Argument argument = new AddCrmOrderDto.Argument();
//
//            CrmOrderDetailInfo crmOrderDetailInfo = new CrmOrderDetailInfo();
//            crmOrderDetailInfo.setOrderId(registerObject.getCustomer().get_id());
//            crmOrderDetailInfo.setEnterpriseAccount(finalEnterpriseAccount);
//            crmOrderDetailInfo.setOrderTpye(registerObject.getOrder().getOrderType());
//
//            CrmOrderProductInfo crmOrderProductInfo = new CrmOrderProductInfo();
//            crmOrderProductInfo.setProductId(productDefine.getProductId());
//            crmOrderProductInfo.setAllResourceCount(allResourceCount);
//            crmOrderProductInfo.setOrderAmount(registerObject.getOrder().getOrderAmount());
//            crmOrderProductInfo.setQuantity(finalQuality);
//            crmOrderProductInfo.setBeginTime(DateUtil.date2Timestamp(registerObject.getOrder().getCrmBeginDate()));
//            crmOrderProductInfo.setEndTime(DateUtil.date2Timestamp(registerObject.getOrder().getCrmEndDate()));
//
//            argument.setCrmOrderDetailInfo(crmOrderDetailInfo);
//            argument.setCrmOrderProductInfo(crmOrderProductInfo);
//
//            AddCrmOrderDto.Result result = versionRegisterService.addCrmOrder(argument);
//            log.info("createOrder success. argument:{}, result:{}", argument, result);
//
//            CrmOrderToDetailDto.Argument createOrderDetailArg = new CrmOrderToDetailDto.Argument();
//            createOrderDetailArg.setOrderId(result.getOrderId());
//            CrmOrderToDetailDto.Result detail = versionRegisterService.crmOrderToDetail(createOrderDetailArg);
//            log.info("crmOrderToDetail success. createOrderDetailArg:{}, result:{}", createOrderDetailArg, detail);
//        });
//    }
//
//    /**
//     * 从配置中心动态获取订单和版本的关系
//     */
//    public List<ProductDefine> getProductDefineList(String appId, String editionId) {
//        List<ProductDefine> result = Lists.newArrayList();
//        Map<String, Map<String, List<String>>> createOrderMappingMap = new Gson().fromJson(createOrderMapping, new TypeToken<Map<String, Map<String, List<String>>>>() {}.getType());
//        Map<String, List<String>> editionMap = createOrderMappingMap.get(appId);
//        if (MapUtils.isEmpty(editionMap)) {
//            log.warn("get getProductDefineList failed. appId:{}, editionId:{}, createOrderMappingMap:{}", appId, editionId, createOrderMappingMap);
//            return Collections.emptyList();
//        }
//        List<String> productDefineNameList = editionMap.get(editionId);
//        if (CollectionUtils.isEmpty(productDefineNameList)) {
//            log.warn("get getProductDefineList failed. appId:{}, productDefineNameList:{}, createOrderMappingMap:{}", appId, productDefineNameList, createOrderMappingMap);
//            return Collections.emptyList();
//        }
//        productDefineNameList.forEach(name -> result.add(productDefineMapping.get(name)));
//        log.info("getProductDefineList success. appId:{}, editionId:{}, productDefineNameList:{}, result:{}", appId,
//                editionId, productDefineNameList, result);
//        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
//    }
//
//    public void createThread(String openInfo, RegisterObject registerObject) {
//        /*QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        String appId = qyweixinAddEnterprise.getAppId();
//        String source = Objects.equals(eserviceAppId, appId) ? CrmRegisterThread.Source.qywx_eservice : CrmRegisterThread.Source.qywx;
//        CrmRegisterThread threadObject = new CrmRegisterThread();
//
//        threadObject.setName(UUID.randomUUID().toString());
//        threadObject.setCompanyName(registerObject.getCustomer().getName());
//        threadObject.setEnterpriseAccount(registerObject.getCustomer().getEnterpriseAccount());
//        threadObject.setManagerName(registerObject.getOrder().getOperatorName());
//        threadObject.setMobile(registerObject.getOrder().getOperatorPhone());
//
//        threadObject.setCertification(
//                Objects.equals(registerObject.getCustomer().getCorpType(), "verified") ?
//                        CrmRegisterThread.IsCertification.Yes :
//                        CrmRegisterThread.IsCertification.No);
//        threadObject.setCompanySize(StringUtils.defaultString(registerObject.getCustomer().getScale(), ""));
//        threadObject.setIndustry(StringUtils.defaultString(registerObject.getCustomer().getIndustry(), ""));
//        threadObject.setSubIndustry(StringUtils.defaultString(registerObject.getCustomer().getSubIndustry(), ""));
//        threadObject.setOpeningTime(System.currentTimeMillis());
//        threadObject.setOwner(Lists.newArrayList(WechatRegisterConfigHelper.getWechatCustomerOwner()));
//        threadObject.setSource(source);
//        String result = crmObjectService.createRegisterThreadObject(threadObject);
//        log.info("createRegisterThreadObject success. argument:{}, result:{}", threadObject, result);*/
//    }
//
//    public void createCustomer(String openInfo, RegisterObject registerObject) {
//        String customerId;
//
//        // 先通过eid获取客户，如果客户不存在，创建客户
//       /* List<CrmCustomer> customerObjects = crmObjectService.getCustomerByKey(CRM_CUSTOMER_OBJECT_CUSTOMER_EID, registerObject.getCustomer().getEnterpriseOpenId());
//        if (CollectionUtils.isEmpty(customerObjects)) {
//            // 客户名称不能重复
//            String customerName = registerObject.getCustomer().getName();
//            customerObjects = crmObjectService.getCustomerByKey(CRM_CUSTOMER_OBJECT_CUSTOMER_NAME, customerName);
//            if (CollectionUtils.isNotEmpty(customerObjects)) {
//                customerName += random.nextInt(99999);
//            }
//
//            CrmCustomer customerObject = new CrmCustomer();
//            customerObject.setName(customerName);
//            customerObject.setEnterpriseAccount(registerObject.getCustomer().getEnterpriseAccount());
//            customerObject.setCustomerEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//            customerObject.setManagerName(registerObject.getOrder().getOperatorName());
//            customerObject.setManagerMobile(registerObject.getOrder().getOperatorPhone());
//            customerObject.setSource(CrmCustomerSource.WE_CHAT.getValue());
//            customerObject.setOutEid(registerObject.getCustomer().getEnterpriseOpenId());
//            customerObject.setOwner(Lists.newArrayList(WechatRegisterConfigHelper.getWechatCustomerOwner()));
//            customerId = crmObjectService.createCustomer(customerObject);
//        } else {
//            CrmCustomer customerObject = customerObjects.get(0);
//
//            if (StringUtils.isNotEmpty(customerObject.getEnterpriseAccount()) || Objects.nonNull(customerObject.getEnterpriseId())) {
//                throw new RuntimeException("通过eid找到的客户下已经存在企业账号或者企业ID");
//            }
//
//            customerId = customerObject.getId();
//
//            // 先创建企业，再创建客户，需要更新客户企业ID，企业账号
//            customerObject.setEnterpriseAccount(registerObject.getCustomer().getEnterpriseAccount());
//            customerObject.setCustomerEnterpriseId(registerObject.getCustomer().getEnterpriseId());
//            customerObject.setManagerName(registerObject.getOrder().getOperatorName());
//            customerObject.setManagerMobile(registerObject.getOrder().getOperatorPhone());
//            customerObject.setSource(CrmCustomerSource.WE_CHAT.getValue());
//            customerObject.setOwner(Lists.newArrayList(WechatRegisterConfigHelper.getWechatCustomerOwner()));
//
//            crmObjectService.updateCustomerInfo(customerObject);
//        }
//
//        registerObject.getCustomer().set_id(customerId);
//        enterpriseDao.updateRegisterObject(resourceSource.getWebhookSource(), registerObject.getCustomer().getEnterpriseOpenId(), JSON.toJSONString(registerObject));*/
//    }
//
//    @Override
//    public void sendWelcomeTask(String openInfo, RegisterObject registerObject) {
//        String fsEA = registerObject.getCustomer().getEnterpriseAccount();
//        List<String> userIds = getAllUserIds(registerObject.getCustomer().getEnterpriseId());
//        sendWelcomeMessage(fsEA, userIds);
//    }
//
//    public void sendWelcomeMessage(String fsEA, List<String> userIds) {
//        WechatRegisterConfigHelper.getWechatMessageValue().forEach((key, value) -> {
//            wechatMessageService.sendMessage(
//                    fsEA,
//                    value.getTitle(),
//                    value.getUrl(),
//                    value.getPicUrl(),
//                    userIds
//            );
//        });
//    }
//
//    @Override
//    public void setSaleRole(String openInfo, RegisterObject registerObject) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        String appId = qyweixinAddEnterprise.getAppId();
//        String ea = registerObject.getCustomer().getEnterpriseAccount();
//        List<String> userIds = getAllUserIds(registerObject.getCustomer().getEnterpriseId());
//
//        // 根据应用设置角色
//        CRMUserRoleResult crmUserRoleResult = userRoleUtils.setUserRole(ea, appId, userIds);
//
//        // 开通企业时，CRM可能还没有初始化好，即配额不足*********，2分钟内重试
//        int retryCount = 0;
//        while ((crmUserRoleResult.getData() == null || crmUserRoleResult.getData().getCode() == *********) && retryCount < 60) {
//            retryCount++;
//            try {
//                Thread.sleep(2000);
//            } catch (Exception ignored) {
//            }
//            crmUserRoleResult = userRoleUtils.setUserRole(ea, appId, getAllUserIds(registerObject.getCustomer().getEnterpriseId()));
//        }
//    }
//
//    @Override
//    public void sendLaunchSM(String openInfo, RegisterObject registerObject) {
//        if (StringUtils.isNotBlank(registerObject.getOrder().getOperatorPhone())) {
//            String mobile = registerObject.getOrder().getOperatorPhone();
//            String smsContent = WechatRegisterConfigHelper.getValidateCodeValue()
//                    .replace("{enterpriseAccount}", registerObject.getCustomer().getEnterpriseAccount())
//                    .replace("{account}", mobile).replace("{password}", registerObject.getCustomer().getPassword());
//
//            smsService.sendMessage(mobile, smsContent);
//        }
//    }
//
//    @Override
//    public void reOpenEnterprise(String oldOpenInfo, String openInfo, RegisterObject registerObject) {
//        QyweixinEnterpriseOrder qyweixinAddEnterprise = JSON.parseObject(openInfo, QyweixinEnterpriseOrder.class);
//        if (Objects.isNull(qyweixinAddEnterprise.getOrderType())) {
//            // 同步可见范围
//            qiyeweixinOrganizationService.updateOrganizationForReOpen(openInfo);
//        } else {
//            syncToHJOrPaas(openInfo, registerObject);
//        }
//    }
//
//    @Override
//    public void syncEnterpriseInfo(String eid) {
//
//    }
//
//    private List<String> getAllUserIds(Integer enterpriseId) {
//        GetAllEmployeeIdsArg getAllEmployeeIdsArg = new GetAllEmployeeIdsArg();
//        getAllEmployeeIdsArg.setRunStatus(RunStatus.ACTIVE);
//        getAllEmployeeIdsArg.setEnterpriseId(enterpriseId);
//        getAllEmployeeIdsArg.setCurrentEmployeeId(OPERATOR.getOperatorId());
//        GetAllEmployeeIdsResult getAllEmployeeIdsResult = employeeService.getAllEmployeeIds(getAllEmployeeIdsArg);
//        return getAllEmployeeIdsResult.getEmployeeIds().stream().distinct().map(String::valueOf).collect(Collectors.toList());
//    }
//
//    @Override
//    public List<TaskFunction<String, RegisterObject>> getProcessorTasks() {
//        return processorTasks;
//    }
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        processorTasks = Lists.newArrayList(
//                this::saveOutData,
//                this::initRegisterObject,
//                this::createEnterprise,
//                this::createCustomer,
//                this::createThread,
//                this::createOrder,
//                this::updateEnterpriseRunstatusToNormal,
//                this::createAdmin,
//                this::sendOpenMessage,
//                this::adminBinder,
//                this::setEnterpriseConfig,
//                this::sendLaunchSM,
//                this::createDB,
//                this::initDBData,
//                this::lanuchEnterprise,
//                this::syncDepartmentTask,
//                this::syncEmployeeTask,
//                this::setSaleRole,
//                this::sendWelcomeTask
//        );
//    }
//}
