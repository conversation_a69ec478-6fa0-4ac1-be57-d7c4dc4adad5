package com.facishare.open.qywx.accountsync.model.qyweixin.bo;

import com.github.mybatis.entity.IdEntity;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/08/16
 */
@Data
@Table(name = "miniprogram_session_key")
public class QyweixinMiniprogramSessionKeyBo extends IdEntity implements Serializable {

    private String corpId;
    //小程序关联的应用id
    private String appId;
    private String userId;
    private String sessionKey;
}
